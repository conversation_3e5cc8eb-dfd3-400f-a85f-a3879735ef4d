import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/auth_service.dart';

class EmailVerificationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Collection name for verification codes
  static const String _verificationCollection = 'email_verifications';
  
  // Code expiration time (10 minutes)
  static const Duration _codeExpiration = Duration(minutes: 10);
  
  // Maximum verification attempts
  static const int _maxAttempts = 5;
  
  // Minimum time between resend requests (60 seconds)
  static const Duration _resendCooldown = Duration(seconds: 60);

  /// Generate a 6-digit verification code
  String _generateVerificationCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }

  /// Send verification code to email and store in Firestore
  Future<void> sendVerificationCode(String email) async {
    try {
      print('DEBUG: sendVerificationCode called for $email');

      // TEMPORARY: Skip Firestore operations for debugging
      // TODO: Re-enable Firestore operations once debugging is complete

      // Generate new verification code
      final code = _generateVerificationCode();
      print('DEBUG: Generated code: $code');

      // Store verification code in memory for testing
      _tempCodes[email] = {
        'code': code,
        'expiresAt': DateTime.now().add(_codeExpiration),
        'attempts': 0,
      };

      // Send email with verification code
      await _sendVerificationEmail(email, code);

      print('DEBUG: sendVerificationCode completed successfully');
    } catch (e) {
      print('DEBUG: Error in sendVerificationCode: $e');
      if (e is AuthException) {
        rethrow;
      }
      throw AuthException('Failed to send verification code', 'send-failed');
    }
  }

  // Temporary storage for testing
  static final Map<String, Map<String, dynamic>> _tempCodes = {};

  /// Verify the provided code against stored code
  Future<bool> verifyCode(String email, String providedCode) async {
    try {
      print('DEBUG: verifyCode called for $email with code $providedCode');

      // TEMPORARY: Use in-memory storage for debugging
      final codeData = _tempCodes[email];

      if (codeData == null) {
        print('DEBUG: No verification code found for $email');
        throw const AuthException('No verification code found', 'code-not-found');
      }

      final storedCode = codeData['code'] as String;
      final expiresAt = codeData['expiresAt'] as DateTime;
      final attempts = codeData['attempts'] as int;

      print('DEBUG: Stored code: $storedCode, Provided code: $providedCode');

      // Check if code has expired
      if (DateTime.now().isAfter(expiresAt)) {
        _tempCodes.remove(email);
        print('DEBUG: Code has expired');
        throw const AuthException('Verification code has expired', 'code-expired');
      }

      // Check if maximum attempts exceeded
      if (attempts >= _maxAttempts) {
        _tempCodes.remove(email);
        print('DEBUG: Maximum attempts exceeded');
        throw const AuthException('Maximum verification attempts exceeded', 'max-attempts');
      }

      // Increment attempt count
      _tempCodes[email]!['attempts'] = attempts + 1;

      // Verify code
      if (storedCode == providedCode) {
        // Code is correct, clean up and return success
        _tempCodes.remove(email);
        print('DEBUG: Code verification successful');
        return true;
      } else {
        print('DEBUG: Invalid verification code');
        throw const AuthException('Invalid verification code', 'invalid-code');
      }

    } catch (e) {
      print('DEBUG: Error in verifyCode: $e');
      if (e is AuthException) {
        rethrow;
      }
      throw AuthException('Failed to verify code', 'verify-failed');
    }
  }

  /// Store verification code in Firestore
  Future<void> _storeVerificationCode(String email, String code, DateTime expiresAt) async {
    await _firestore.collection(_verificationCollection).doc(email).set({
      'code': code,
      'email': email,
      'expiresAt': Timestamp.fromDate(expiresAt),
      'createdAt': Timestamp.now(),
      'lastSent': Timestamp.now(),
      'attempts': 0,
    });
  }

  /// Get verification document from Firestore
  Future<DocumentSnapshot?> _getVerificationDoc(String email) async {
    try {
      return await _firestore.collection(_verificationCollection).doc(email).get();
    } catch (e) {
      return null;
    }
  }

  /// Delete verification document from Firestore
  Future<void> _deleteVerificationDoc(String email) async {
    try {
      await _firestore.collection(_verificationCollection).doc(email).delete();
    } catch (e) {
      // Ignore deletion errors
    }
  }

  /// Increment verification attempts
  Future<void> _incrementAttempts(String email) async {
    try {
      await _firestore.collection(_verificationCollection).doc(email).update({
        'attempts': FieldValue.increment(1),
      });
    } catch (e) {
      // Ignore increment errors
    }
  }

  /// Send verification email (placeholder - will be implemented with actual email service)
  Future<void> _sendVerificationEmail(String email, String code) async {
    // TODO: Implement actual email sending
    // For now, we'll use Firebase Auth's built-in email verification
    // but customize the message to include the 6-digit code
    
    // This is a placeholder implementation
    // In a real app, you would use:
    // - Firebase Functions to send custom emails
    // - Third-party email service (SendGrid, Mailgun, etc.)
    // - Custom email server
    
    print('Verification code for $email: $code'); // For testing purposes
    
    // For now, we'll simulate email sending
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Clean up expired verification codes (should be called periodically)
  Future<void> cleanupExpiredCodes() async {
    try {
      final now = Timestamp.now();
      final expiredQuery = await _firestore
          .collection(_verificationCollection)
          .where('expiresAt', isLessThan: now)
          .get();
      
      final batch = _firestore.batch();
      for (final doc in expiredQuery.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
    } catch (e) {
      // Ignore cleanup errors
    }
  }

  /// Get remaining time until code expires
  Future<Duration?> getRemainingTime(String email) async {
    try {
      final doc = await _getVerificationDoc(email);
      if (doc == null || !doc.exists) return null;
      
      final data = doc.data() as Map<String, dynamic>;
      final expiresAt = (data['expiresAt'] as Timestamp).toDate();
      final now = DateTime.now();
      
      if (now.isAfter(expiresAt)) return null;
      
      return expiresAt.difference(now);
    } catch (e) {
      return null;
    }
  }

  /// Get remaining attempts
  Future<int> getRemainingAttempts(String email) async {
    try {
      final doc = await _getVerificationDoc(email);
      if (doc == null || !doc.exists) return _maxAttempts;
      
      final data = doc.data() as Map<String, dynamic>;
      final attempts = data['attempts'] as int? ?? 0;
      
      return (_maxAttempts - attempts).clamp(0, _maxAttempts);
    } catch (e) {
      return _maxAttempts;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';


// Notification model
class NotificationItem {
  final String id;
  final String title;
  final String message;
  final String time;
  final IconData icon;
  final Color iconColor;
  final String badge;
  final Color badgeColor;
  final bool isRead;
  final NotificationType type;

  NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.time,
    required this.icon,
    required this.iconColor,
    required this.badge,
    required this.badgeColor,
    required this.isRead,
    required this.type,
  });
}

enum NotificationType { message, alert, update, system, reminder, welcome }

// Sample notifications data
final notificationsProvider = StateProvider<List<NotificationItem>>((ref) => [
  NotificationItem(
    id: '1',
    title: 'New Message',
    message: 'You have received a new message from John <PERSON>e. Tap to view the full conversation.',
    time: '2 min ago',
    icon: Icons.message_outlined,
    iconColor: const Color(0xFFe92933),
    badge: 'message',
    badgeColor: const Color(0xFFe92933),
    isRead: false,
    type: NotificationType.message,
  ),
  NotificationItem(
    id: '2',
    title: 'Security Alert',
    message: 'Unusual login activity detected from a new device. Please verify if this was you.',
    time: '5 min ago',
    icon: Icons.warning_outlined,
    iconColor: const Color(0xFFFF9800),
    badge: 'alert',
    badgeColor: const Color(0xFFFF9800),
    isRead: false,
    type: NotificationType.alert,
  ),
  NotificationItem(
    id: '3',
    title: 'App Update Available',
    message: 'Version 2.1.0 is now available with new features and bug fixes.',
    time: '1 hour ago',
    icon: Icons.notifications_outlined,
    iconColor: const Color(0xFF4CAF50),
    badge: 'update',
    badgeColor: const Color(0xFF4CAF50),
    isRead: true,
    type: NotificationType.update,
  ),
  NotificationItem(
    id: '4',
    title: 'System Maintenance',
    message: 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM.',
    time: '2 hours ago',
    icon: Icons.settings_outlined,
    iconColor: const Color(0xFF637488),
    badge: 'system',
    badgeColor: const Color(0xFF637488),
    isRead: false,
    type: NotificationType.system,
  ),
  NotificationItem(
    id: '5',
    title: 'Policy Renewal Reminder',
    message: 'Your health insurance policy expires in 30 days. Renew now to avoid coverage gaps.',
    time: '1 day ago',
    icon: Icons.calendar_today_outlined,
    iconColor: const Color(0xFF2196F3),
    badge: 'reminder',
    badgeColor: const Color(0xFF2196F3),
    isRead: false,
    type: NotificationType.reminder,
  ),
  NotificationItem(
    id: '6',
    title: 'Welcome to All About Insurance',
    message: 'Thank you for joining us! Explore our features and find the perfect insurance coverage.',
    time: '3 days ago',
    icon: Icons.person_outline,
    iconColor: const Color(0xFF4CAF50),
    badge: 'welcome',
    badgeColor: const Color(0xFF4CAF50),
    isRead: true,
    type: NotificationType.welcome,
  ),
]);

class NotificationsScreen extends ConsumerStatefulWidget {
  const NotificationsScreen({super.key});

  @override
  ConsumerState<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends ConsumerState<NotificationsScreen>
    with TickerProviderStateMixin {
  bool _showUnreadOnly = false;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  List<NotificationItem> get _filteredNotifications {
    final notifications = ref.watch(notificationsProvider);
    if (_showUnreadOnly) {
      return notifications.where((n) => !n.isRead).toList();
    }
    return notifications;
  }

  int get _unreadCount {
    final notifications = ref.watch(notificationsProvider);
    return notifications.where((n) => !n.isRead).length;
  }

  void _markAllAsRead() {
    final notifications = ref.read(notificationsProvider.notifier);
    final updatedNotifications = ref.read(notificationsProvider).map((n) {
      return NotificationItem(
        id: n.id,
        title: n.title,
        message: n.message,
        time: n.time,
        icon: n.icon,
        iconColor: n.iconColor,
        badge: n.badge,
        badgeColor: n.badgeColor,
        isRead: true,
        type: n.type,
      );
    }).toList();
    notifications.state = updatedNotifications;
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('All notifications marked as read'),
        backgroundColor: Color(0xFF4CAF50),
      ),
    );
  }

  void _toggleNotificationRead(String id) {
    final notifications = ref.read(notificationsProvider.notifier);
    final updatedNotifications = ref.read(notificationsProvider).map((n) {
      if (n.id == id) {
        return NotificationItem(
          id: n.id,
          title: n.title,
          message: n.message,
          time: n.time,
          icon: n.icon,
          iconColor: n.iconColor,
          badge: n.badge,
          badgeColor: n.badgeColor,
          isRead: !n.isRead,
          type: n.type,
        );
      }
      return n;
    }).toList();
    notifications.state = updatedNotifications;
  }

  Widget _buildNotificationItem(NotificationItem notification, int index) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final animation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            index * 0.1,
            1.0,
            curve: Curves.easeOutCubic,
          ),
        ));

        return Transform.translate(
          offset: Offset(0, 20 * (1 - animation.value)),
          child: Opacity(
            opacity: animation.value,
            child: GestureDetector(
              onTap: () {
                if (!notification.isRead) {
                  _toggleNotificationRead(notification.id);
                }
                // TODO: Navigate to notification detail or relevant screen
              },
              child: Container(
                decoration: BoxDecoration(
                  color: notification.isRead
                      ? Colors.white
                      : const Color(0xFFe92933).withOpacity(0.02),
                  border: const Border(
                    bottom: BorderSide(
                      color: Color(0x0F000000),
                      width: 1,
                    ),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Icon
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: notification.iconColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          notification.icon,
                          color: notification.iconColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),

                      // Content
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Title row
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    notification.title,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: notification.isRead
                                          ? FontWeight.w500
                                          : FontWeight.w600,
                                      color: notification.isRead
                                          ? const Color(0xFF111418)
                                          : const Color(0xFF111418),
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                const SizedBox(width: 8),

                                // Unread indicator
                                if (!notification.isRead)
                                  Container(
                                    width: 8,
                                    height: 8,
                                    decoration: const BoxDecoration(
                                      color: Color(0xFFe92933),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                const SizedBox(width: 8),

                                // Badge
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: notification.badgeColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    notification.badge,
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                      color: notification.badgeColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),

                            // Message
                            Text(
                              notification.message,
                              style: TextStyle(
                                fontSize: 13,
                                color: notification.isRead
                                    ? const Color(0xFF637488)
                                    : const Color(0xFF111418).withOpacity(0.8),
                                height: 1.4,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 8),

                            // Time and read status
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  size: 12,
                                  color: const Color(0xFF637488),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  notification.time,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF637488),
                                  ),
                                ),
                                const Spacer(),

                                // Read indicator
                                if (notification.isRead)
                                  const Icon(
                                    Icons.check,
                                    size: 12,
                                    color: Color(0xFF4CAF50),
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(0xFF637488).withOpacity(0.1),
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.notifications_none,
                size: 40,
                color: Color(0xFF637488),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _showUnreadOnly ? 'No unread notifications' : 'No notifications',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF111418),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _showUnreadOnly
                  ? 'All caught up! Check back later for new updates.'
                  : 'You\'ll see notifications here when you receive them.',
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF637488),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filteredNotifications = _filteredNotifications;
    final totalCount = ref.watch(notificationsProvider).length;

    return Scaffold(
      backgroundColor: const Color(0xFFf1f1f1),
      body: Column(
        children: [
          // Header
          Container(
            color: const Color(0xFFf1f1f1),
            child: SafeArea(
              bottom: false,
              child: Container(
                decoration: const BoxDecoration(
                  color: Color(0xFFf1f1f1),
                  border: Border(
                    bottom: BorderSide(
                      color: Color(0x0F000000),
                      width: 1,
                    ),
                  ),
                ),
                child: Column(
                  children: [
                    // Top header row
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          // Back button
                          GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: Container(
                              width: 36,
                              height: 36,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: Colors.transparent,
                              ),
                              child: const Icon(
                                Icons.arrow_back_ios,
                                color: Color(0xFFe92933),
                                size: 20,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          
                          // Title and badge
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Notifications',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF111418),
                                  ),
                                ),
                                if (_unreadCount > 0) ...[
                                  const SizedBox(height: 4),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFe92933).withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      '$_unreadCount unread',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xFFe92933),
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                          
                          // Action buttons
                          Row(
                            children: [
                              // Filter button
                              GestureDetector(
                                onTap: () {
                                  // TODO: Implement filter functionality
                                },
                                child: Container(
                                  width: 36,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    color: Colors.transparent,
                                  ),
                                  child: const Icon(
                                    Icons.filter_list,
                                    color: Color(0xFF637488),
                                    size: 20,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              
                              // Mark all read button
                              GestureDetector(
                                onTap: _unreadCount > 0 ? _markAllAsRead : null,
                                child: Container(
                                  width: 36,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    color: Colors.transparent,
                                  ),
                                  child: Icon(
                                    Icons.done_all,
                                    color: _unreadCount > 0 
                                        ? const Color(0xFF637488)
                                        : const Color(0xFF637488).withOpacity(0.5),
                                    size: 20,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    
                    // Tabs
                    Container(
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(
                            color: Color(0x0F000000),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          // All tab
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _showUnreadOnly = false;
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                      color: !_showUnreadOnly 
                                          ? const Color(0xFFe92933)
                                          : Colors.transparent,
                                      width: 2,
                                    ),
                                  ),
                                  color: !_showUnreadOnly 
                                      ? const Color(0xFFe92933).withOpacity(0.05)
                                      : Colors.transparent,
                                ),
                                child: Text(
                                  'All ($totalCount)',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: !_showUnreadOnly 
                                        ? const Color(0xFFe92933)
                                        : const Color(0xFF637488),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          
                          // Unread tab
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _showUnreadOnly = true;
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                      color: _showUnreadOnly 
                                          ? const Color(0xFFe92933)
                                          : Colors.transparent,
                                      width: 2,
                                    ),
                                  ),
                                  color: _showUnreadOnly 
                                      ? const Color(0xFFe92933).withOpacity(0.05)
                                      : Colors.transparent,
                                ),
                                child: Text(
                                  'Unread ($_unreadCount)',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: _showUnreadOnly 
                                        ? const Color(0xFFe92933)
                                        : const Color(0xFF637488),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Notifications list
          Expanded(
            child: filteredNotifications.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: filteredNotifications.length,
                    itemBuilder: (context, index) {
                      final notification = filteredNotifications[index];
                      return _buildNotificationItem(notification, index);
                    },
                  ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/auth_provider.dart';
import '../../providers/settings_provider.dart';
import '../../services/navigation_service.dart';
import '../../widgets/settings/settings_header.dart';
import '../../widgets/settings/settings_search_bar.dart';
import '../../widgets/settings/settings_section.dart';
import '../../widgets/settings/settings_item.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
  }

  bool _shouldShowItem(String title, String description) {
    if (_searchQuery.isEmpty) return true;
    return title.toLowerCase().contains(_searchQuery) ||
           description.toLowerCase().contains(_searchQuery);
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    final settingsState = ref.watch(settingsProvider);
    
    return Scaffold(
      backgroundColor: const Color(0xFFf1f1f1),
      body: Column(
        children: [
          // Header with gradient background
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF012853),
                  Color(0xFF1b3e64),
                ],
              ),
            ),
            child: SafeArea(
              bottom: false,
              child: Column(
                children: [
                  // App Bar
                  Container(
                    height: 56,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      children: [
                        IconButton(
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                            size: 20,
                          ),
                          onPressed: () => Navigator.pop(context),
                        ),
                        const Expanded(
                          child: Text(
                            'Settings',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 40), // Balance the back button
                      ],
                    ),
                  ),
                  
                  // User Profile Header
                  SettingsHeader(
                    userName: currentUser?.displayName ?? 'Alex Johnson',
                    userEmail: currentUser?.email ?? '<EMAIL>',
                    userImageUrl: currentUser?.photoURL,
                    isPremium: true,
                  ),
                ],
              ),
            ),
          ),
          
          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const SizedBox(height: 8),
                  
                  // Search Bar
                  SettingsSearchBar(
                    controller: _searchController,
                    onChanged: _onSearchChanged,
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Account Section
                  if (_shouldShowItem('Profile', 'Manage your profile information') ||
                      _shouldShowItem('Subscription', 'Manage your premium subscription') ||
                      _shouldShowItem('Security', 'Password and security settings'))
                    SettingsSection(
                      title: 'Account',
                      children: [
                        if (_shouldShowItem('Profile', 'Manage your profile information'))
                          SettingsItem(
                            icon: Icons.person_outline,
                            title: 'Profile',
                            description: 'Manage your profile information',
                            onTap: () => NavigationService.instance.navigateToProfile(),
                          ),
                        if (_shouldShowItem('Subscription', 'Manage your premium subscription'))
                          SettingsItem(
                            icon: Icons.credit_card_outlined,
                            title: 'Subscription',
                            description: 'Manage your premium subscription',
                            onTap: () => NavigationService.instance.navigateToSubscription(),
                          ),
                        if (_shouldShowItem('Security', 'Password and security settings'))
                          SettingsItem(
                            icon: Icons.lock_outline,
                            title: 'Security',
                            description: 'Password and security settings',
                            onTap: () {
                              // TODO: Navigate to security settings
                            },
                          ),
                      ],
                    ),
                  
                  const SizedBox(height: 24),
                  
                  // Notifications Section
                  if (_shouldShowItem('Push Notifications', 'Receive notifications on your device') ||
                      _shouldShowItem('Email Notifications', 'Receive updates via email'))
                    SettingsSection(
                      title: 'Notifications',
                      children: [
                        if (_shouldShowItem('Push Notifications', 'Receive notifications on your device'))
                          SettingsItem(
                            icon: Icons.notifications_outlined,
                            title: 'Push Notifications',
                            description: 'Receive notifications on your device',
                            trailing: Switch(
                              value: settingsState.pushNotificationsEnabled,
                              onChanged: (value) {
                                ref.read(settingsProvider.notifier).setPushNotifications(value);
                              },
                              activeColor: const Color(0xFFe92933),
                            ),
                          ),
                        if (_shouldShowItem('Email Notifications', 'Receive updates via email'))
                          SettingsItem(
                            icon: Icons.email_outlined,
                            title: 'Email Notifications',
                            description: 'Receive updates via email',
                            trailing: Switch(
                              value: settingsState.emailNotificationsEnabled,
                              onChanged: (value) {
                                ref.read(settingsProvider.notifier).setEmailNotifications(value);
                              },
                              activeColor: const Color(0xFFe92933),
                            ),
                          ),
                      ],
                    ),
                  
                  const SizedBox(height: 24),
                  
                  // Privacy Section
                  if (_shouldShowItem('Location Services', 'Allow location access for better experience') ||
                      _shouldShowItem('Analytics', 'Help improve the app by sharing usage data'))
                    SettingsSection(
                      title: 'Privacy',
                      children: [
                        if (_shouldShowItem('Location Services', 'Allow location access for better experience'))
                          SettingsItem(
                            icon: Icons.location_on_outlined,
                            title: 'Location Services',
                            description: 'Allow location access for better experience',
                            trailing: Switch(
                              value: settingsState.locationServicesEnabled,
                              onChanged: (value) {
                                ref.read(settingsProvider.notifier).setLocationServices(value);
                              },
                              activeColor: const Color(0xFFe92933),
                            ),
                          ),
                        if (_shouldShowItem('Analytics', 'Help improve the app by sharing usage data'))
                          SettingsItem(
                            icon: Icons.shield_outlined,
                            title: 'Analytics',
                            description: 'Help improve the app by sharing usage data',
                            trailing: Switch(
                              value: settingsState.analyticsEnabled,
                              onChanged: (value) {
                                ref.read(settingsProvider.notifier).setAnalytics(value);
                              },
                              activeColor: const Color(0xFFe92933),
                            ),
                          ),
                      ],
                    ),
                  
                  const SizedBox(height: 24),
                  
                  // Preferences Section
                  if (_shouldShowItem('Dark Mode', 'Switch to dark theme') ||
                      _shouldShowItem('Biometric Authentication', 'Use fingerprint or face recognition') ||
                      _shouldShowItem('Auto Download', 'Automatically download updates'))
                    SettingsSection(
                      title: 'Preferences',
                      children: [
                        if (_shouldShowItem('Dark Mode', 'Switch to dark theme'))
                          SettingsItem(
                            icon: Icons.dark_mode_outlined,
                            title: 'Dark Mode',
                            description: 'Switch to dark theme',
                            trailing: Switch(
                              value: settingsState.darkModeEnabled,
                              onChanged: (value) {
                                ref.read(settingsProvider.notifier).setDarkMode(value);
                              },
                              activeColor: const Color(0xFFe92933),
                            ),
                          ),
                        if (_shouldShowItem('Biometric Authentication', 'Use fingerprint or face recognition'))
                          SettingsItem(
                            icon: Icons.fingerprint_outlined,
                            title: 'Biometric Authentication',
                            description: 'Use fingerprint or face recognition',
                            trailing: Switch(
                              value: settingsState.biometricEnabled,
                              onChanged: (value) {
                                ref.read(settingsProvider.notifier).setBiometric(value);
                              },
                              activeColor: const Color(0xFFe92933),
                            ),
                          ),
                        if (_shouldShowItem('Auto Download', 'Automatically download updates'))
                          SettingsItem(
                            icon: Icons.download_outlined,
                            title: 'Auto Download',
                            description: 'Automatically download updates',
                            trailing: Switch(
                              value: settingsState.autoDownloadEnabled,
                              onChanged: (value) {
                                ref.read(settingsProvider.notifier).setAutoDownload(value);
                              },
                              activeColor: const Color(0xFFe92933),
                            ),
                          ),
                      ],
                    ),
                  
                  const SizedBox(height: 24),
                  
                  // Support Section
                  if (_shouldShowItem('Help Center', 'Get help and support') ||
                      _shouldShowItem('About', 'App version and information'))
                    SettingsSection(
                      title: 'Support',
                      children: [
                        if (_shouldShowItem('Help Center', 'Get help and support'))
                          SettingsItem(
                            icon: Icons.help_outline,
                            title: 'Help Center',
                            description: 'Get help and support',
                            onTap: () {
                              // TODO: Navigate to help center
                            },
                          ),
                        if (_shouldShowItem('About', 'App version and information'))
                          SettingsItem(
                            icon: Icons.info_outline,
                            title: 'About',
                            description: 'App version and information',
                            onTap: () {
                              // TODO: Navigate to about page
                            },
                          ),
                      ],
                    ),
                  
                  const SizedBox(height: 24),
                  
                  // Account Actions Section
                  SettingsSection(
                    title: 'Account Actions',
                    children: [
                      SettingsItem(
                        icon: Icons.logout,
                        title: 'Sign Out',
                        description: 'Sign out of your account',
                        iconColor: Colors.red,
                        titleColor: Colors.red,
                        onTap: () {
                          _showSignOutDialog(context);
                        },
                      ),
                      SettingsItem(
                        icon: Icons.delete_outline,
                        title: 'Delete Account',
                        description: 'Permanently delete your account',
                        iconColor: Colors.red,
                        titleColor: Colors.red,
                        onTap: () {
                          _showDeleteAccountDialog(context);
                        },
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showSignOutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(authProvider.notifier).signOut();
              NavigationService.instance.navigateToAlternateSignIn();
            },
            child: const Text('Sign Out', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: const Text(
          'Are you sure you want to permanently delete your account? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement account deletion
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

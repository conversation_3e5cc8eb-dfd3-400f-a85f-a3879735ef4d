import 'package:shared_preferences/shared_preferences.dart';

class OnboardingService {
  static const String _onboardingCompleteKey = 'onboarding_complete';
  static const String _firstLaunchKey = 'first_launch';
  
  static OnboardingService? _instance;
  static OnboardingService get instance => _instance ??= OnboardingService._();
  
  OnboardingService._();
  
  /// Check if the user has completed onboarding
  Future<bool> isOnboardingComplete() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_onboardingCompleteKey) ?? false;
  }
  
  /// Mark onboarding as complete
  Future<void> completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_onboardingCompleteKey, true);
  }
  
  /// Check if this is the first app launch
  Future<bool> isFirstLaunch() async {
    final prefs = await SharedPreferences.getInstance();
    final isFirst = prefs.getBool(_firstLaunchKey) ?? true;
    
    if (isFirst) {
      await prefs.setBool(_firstLaunch<PERSON><PERSON>, false);
    }
    
    return isFirst;
  }
  
  /// Reset onboarding status (useful for testing)
  Future<void> resetOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_onboardingCompleteKey);
    await prefs.remove(_firstLaunchKey);
  }
  
  /// Check if user should see onboarding
  Future<bool> shouldShowOnboarding() async {
    final isComplete = await isOnboardingComplete();
    return !isComplete;
  }
}

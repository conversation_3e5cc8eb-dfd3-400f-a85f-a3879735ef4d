import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class TermsScreen extends StatelessWidget {
  const TermsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            // Header
            Container(
              height: 56,
              decoration: const BoxDecoration(
                color: Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFe0e0e0),
                    width: 1.0,
                  ),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(
                        Icons.close,
                        color: Color(0xFFe92933),
                        size: 24,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        'Terms of Service',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF333333),
                        ),
                      ),
                    ),
                    const SizedBox(width: 48), // Balance the close button
                  ],
                ),
              ),
            ),
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24.0, 32.0, 24.0, 32.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(
                      title: '1. Acceptance of Terms',
                      content: 'By accessing and using All About Insurance mobile application, you accept and agree to be bound by the terms and provision of this agreement.',
                    ),

                    _buildSection(
                      title: '2. Use License',
                      content: 'Permission is granted to temporarily download one copy of All About Insurance app for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title.',
                    ),

                    _buildSection(
                      title: '3. Disclaimer',
                      content: 'The materials on All About Insurance app are provided on an \'as is\' basis. All About Insurance makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.',
                    ),

                    _buildSection(
                      title: '4. Limitations',
                      content: 'In no event shall All About Insurance or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on All About Insurance app.',
                    ),

                    _buildSection(
                      title: '5. Privacy Policy',
                      content: 'Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information when you use our service.',
                    ),

                    _buildSection(
                      title: '6. User Accounts',
                      content: 'When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities that occur under your account.',
                    ),

                    _buildSection(
                      title: '7. Insurance Information',
                      content: 'All About Insurance provides general information about insurance products and services. This information is for educational purposes only and should not be considered as professional insurance advice. Always consult with licensed insurance professionals for specific insurance needs.',
                    ),

                    _buildSection(
                      title: '8. Modifications',
                      content: 'All About Insurance may revise these terms of service at any time without notice. By using this app, you are agreeing to be bound by the then current version of these terms of service.',
                    ),

                    _buildContactSection(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '9. Contact Information',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 4),
          RichText(
            text: TextSpan(
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
                height: 1.5,
              ),
              children: [
                const TextSpan(
                  text: 'If you have any questions about these Terms of Service, please contact us at ',
                ),
                WidgetSpan(
                  child: GestureDetector(
                    onTap: () => _launchEmail('<EMAIL>'),
                    child: const Text(
                      '<EMAIL>',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFFe8262f),
                        height: 1.5,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    }
  }
}

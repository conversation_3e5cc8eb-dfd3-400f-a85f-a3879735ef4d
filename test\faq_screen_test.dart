import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aai/screens/faq_screen.dart';

void main() {
  group('FAQScreen Tests', () {
    testWidgets('FAQScreen displays correctly', (WidgetTester tester) async {
      // Build the FAQScreen widget
      await tester.pumpWidget(
        const MaterialApp(
          home: FAQScreen(),
        ),
      );

      // Verify that the screen displays the main elements
      expect(find.text('Frequently Asked Questions'), findsOneWidget);
      expect(find.text('Find answers to common questions about our service'), findsOneWidget);
      
      // Verify help icon is displayed
      expect(find.byIcon(Icons.help_outline), findsOneWidget);
      
      // Verify some FAQ questions are displayed
      expect(find.text('How do I get started?'), findsOneWidget);
      expect(find.text('Is there a mobile app available?'), findsOneWidget);
      expect(find.text('How do I contact customer support?'), findsOneWidget);
      expect(find.text('What are your pricing plans?'), findsOneWidget);
      
      // Verify numbered indicators are displayed
      expect(find.text('01'), findsOneWidget);
      expect(find.text('02'), findsOneWidget);
      expect(find.text('03'), findsOneWidget);
      expect(find.text('04'), findsOneWidget);
    });

    testWidgets('FAQ expansion works', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FAQScreen(),
        ),
      );

      // Initially, answers should not be visible
      expect(find.text('Getting started is easy!'), findsNothing);

      // Tap on the first FAQ item
      await tester.tap(find.text('How do I get started?'));
      await tester.pumpAndSettle();

      // Now the answer should be visible
      expect(find.textContaining('Getting started is easy!'), findsOneWidget);
    });

    testWidgets('Multiple FAQ items can be expanded', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FAQScreen(),
        ),
      );

      // Tap on the first FAQ item
      await tester.tap(find.text('How do I get started?'));
      await tester.pumpAndSettle();

      // Tap on the second FAQ item
      await tester.tap(find.text('Is there a mobile app available?'));
      await tester.pumpAndSettle();

      // Both answers should be visible
      expect(find.textContaining('Getting started is easy!'), findsOneWidget);
      expect(find.textContaining('Yes! Our mobile app is available'), findsOneWidget);
    });

    testWidgets('FAQ items can be collapsed', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FAQScreen(),
        ),
      );

      // Tap to expand
      await tester.tap(find.text('How do I get started?'));
      await tester.pumpAndSettle();

      // Verify answer is visible
      expect(find.textContaining('Getting started is easy!'), findsOneWidget);

      // Tap again to collapse
      await tester.tap(find.text('How do I get started?'));
      await tester.pumpAndSettle();

      // Answer should no longer be visible
      expect(find.textContaining('Getting started is easy!'), findsNothing);
    });

    testWidgets('All FAQ questions are displayed', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FAQScreen(),
        ),
      );

      // Verify all 8 FAQ questions are present
      expect(find.text('How do I get started?'), findsOneWidget);
      expect(find.text('Is there a mobile app available?'), findsOneWidget);
      expect(find.text('How do I contact customer support?'), findsOneWidget);
      expect(find.text('What are your pricing plans?'), findsOneWidget);
      expect(find.text('Is my data secure?'), findsOneWidget);
      expect(find.text('Can I cancel my subscription anytime?'), findsOneWidget);
      expect(find.text('Do you offer refunds?'), findsOneWidget);
      expect(find.text('How do I update my account information?'), findsOneWidget);
    });

    testWidgets('Numbered indicators are correct', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FAQScreen(),
        ),
      );

      // Verify numbered indicators from 01 to 08
      expect(find.text('01'), findsOneWidget);
      expect(find.text('02'), findsOneWidget);
      expect(find.text('03'), findsOneWidget);
      expect(find.text('04'), findsOneWidget);
      expect(find.text('05'), findsOneWidget);
      expect(find.text('06'), findsOneWidget);
      expect(find.text('07'), findsOneWidget);
      expect(find.text('08'), findsOneWidget);
    });

    testWidgets('Scrolling works properly', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FAQScreen(),
        ),
      );

      // Verify we can scroll to see all content
      await tester.scrollUntilVisible(
        find.text('How do I update my account information?'),
        500.0,
      );

      expect(find.text('How do I update my account information?'), findsOneWidget);
    });

    testWidgets('Back button works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const FAQScreen()),
                ),
                child: const Text('Go to FAQ'),
              ),
            ),
          ),
        ),
      );

      // Navigate to FAQ screen
      await tester.tap(find.text('Go to FAQ'));
      await tester.pumpAndSettle();

      // Verify we're on the FAQ screen
      expect(find.text('Frequently Asked Questions'), findsOneWidget);

      // Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Verify we're back to the original screen
      expect(find.text('Go to FAQ'), findsOneWidget);
    });

    testWidgets('FAQ content is comprehensive', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FAQScreen(),
        ),
      );

      // Test a few specific answers by expanding FAQ items
      await tester.tap(find.text('What are your pricing plans?'));
      await tester.pumpAndSettle();

      expect(find.textContaining('flexible pricing plans'), findsOneWidget);
      expect(find.textContaining('\$9.99/month'), findsOneWidget);

      await tester.tap(find.text('Is my data secure?'));
      await tester.pumpAndSettle();

      expect(find.textContaining('industry-standard encryption'), findsOneWidget);
      expect(find.textContaining('GDPR'), findsOneWidget);
    });
  });
}

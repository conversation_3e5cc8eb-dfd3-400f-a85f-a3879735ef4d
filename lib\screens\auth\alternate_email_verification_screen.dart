import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import '../../models/auth_state.dart';
import '../../providers/auth_provider.dart';
import '../../services/navigation_service.dart';

class AlternateEmailVerificationScreen extends ConsumerStatefulWidget {
  final String email;
  final String password;

  const AlternateEmailVerificationScreen({
    super.key,
    required this.email,
    required this.password,
  });

  @override
  ConsumerState<AlternateEmailVerificationScreen> createState() => _AlternateEmailVerificationScreenState();
}

class _AlternateEmailVerificationScreenState extends ConsumerState<AlternateEmailVerificationScreen> {
  bool _canResend = false;
  int _resendCountdown = 60;
  Timer? _resendTimer;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
  }

  @override
  void dispose() {
    _resendTimer?.cancel();
    super.dispose();
  }

  void _startResendTimer() {
    _canResend = false;
    _resendCountdown = 60;
    
    _resendTimer?.cancel();
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_resendCountdown > 0) {
            _resendCountdown--;
          } else {
            _canResend = true;
            timer.cancel();
          }
        });
      }
    });
  }

  void _onResendPressed() {
    if (_canResend) {
      ref.read(authProvider.notifier).resendEmailVerification(
        email: widget.email,
        password: widget.password,
      );
      _startResendTimer();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Center(
            child: Text(
              'Verification email sent!',
              style: TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
          backgroundColor: Colors.blue,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes
    ref.listen<AuthState>(authProvider, (previous, next) {
      if (next.status == AuthStatus.authenticated) {
        // Navigate to home screen immediately for smooth experience
        if (mounted) {
          NavigationService.instance.navigateToHome();
        }
      }
      // Note: We don't listen for AuthStatus.verificationPending here
      // because this screen IS the verification screen - we don't want to navigate away from it
    });

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Main Content - Centered
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Title
                      _buildTitle(),

                      const SizedBox(height: 40),
                      
                      // Instructions Box
                      _buildInstructionsBox(),
                      
                      const SizedBox(height: 32),
                      
                      // Resend Button
                      _buildResendButton(),
                      
                      const SizedBox(height: 24),
                      
                      // Help Text
                      _buildHelpText(),
                    ],
                  ),
                ),
              ),
            ),
            
            // Header - Positioned at top
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: _buildHeader(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          IconButton(
            onPressed: () => NavigationService.instance.goBack(),
            icon: const Icon(
              Icons.arrow_back,
              color: Color(0xFFe92933),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return const Text(
      'Verify Your Email',
      style: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Color(0xFF1a1a1a),
      ),
    );
  }

  Widget _buildInstructionsBox() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.email_outlined,
            size: 48,
            color: Color(0xFFe92933),
          ),
          const SizedBox(height: 16),
          const Text(
            'Please check your email and click the verification link to verify your account.',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF1a1a1a),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          const Text(
            'After clicking the link, you will be automatically signed in.',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF666666),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildResendButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _canResend ? _onResendPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFe92933),
          foregroundColor: Colors.white,
          disabledBackgroundColor: const Color(0xFFe92933), // Keep red when disabled
          disabledForegroundColor: Colors.white, // Keep white text when disabled
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: Text(
          _canResend
              ? 'Resend Email'
              : 'Resend Email (${_resendCountdown}s)',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildHelpText() {
    return const Text(
      'Didn\'t receive the email?\nCheck your spam folder or try resending.',
      style: TextStyle(
        fontSize: 14,
        color: Color(0xFF666666),
      ),
      textAlign: TextAlign.center,
    );
  }
}

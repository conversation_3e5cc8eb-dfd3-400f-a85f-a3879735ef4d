import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../providers/auth_provider.dart';

class EnhancedProfileScreen extends ConsumerStatefulWidget {
  const EnhancedProfileScreen({super.key});

  @override
  ConsumerState<EnhancedProfileScreen> createState() => _EnhancedProfileScreenState();
}

class _EnhancedProfileScreenState extends ConsumerState<EnhancedProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Personal Information Controllers
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  
  // Professional Information Controllers
  final _companyController = TextEditingController();
  final _designationController = TextEditingController();
  final _gstinController = TextEditingController();
  
  // Date of Birth
  DateTime? _selectedDate;
  final _dateController = TextEditingController();
  
  // Dropdown values
  String? _selectedRole;
  String? _selectedLanguage;
  String? _selectedState;
  
  // Images
  File? _profileImage;
  File? _companyLogo;
  
  bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();

  // Dropdown options
  final List<String> _roles = [
    'Insurance Agent',
    'Senior Agent',
    'Team Leader',
    'Branch Manager',
    'Regional Manager',
    'Sales Executive',
    'Customer Service Representative',
  ];

  final List<String> _languages = [
    'Hindi',
    'English',
    'Bengali',
    'Telugu',
    'Marathi',
    'Tamil',
    'Gujarati',
    'Urdu',
    'Kannada',
    'Odia',
    'Malayalam',
    'Punjabi',
  ];

  final List<String> _states = [
    'Andhra Pradesh',
    'Arunachal Pradesh',
    'Assam',
    'Bihar',
    'Chhattisgarh',
    'Goa',
    'Gujarat',
    'Haryana',
    'Himachal Pradesh',
    'Jharkhand',
    'Karnataka',
    'Kerala',
    'Madhya Pradesh',
    'Maharashtra',
    'Manipur',
    'Meghalaya',
    'Mizoram',
    'Nagaland',
    'Odisha',
    'Punjab',
    'Rajasthan',
    'Sikkim',
    'Tamil Nadu',
    'Telangana',
    'Tripura',
    'Uttar Pradesh',
    'Uttarakhand',
    'West Bengal',
    'Delhi',
    'Jammu and Kashmir',
    'Ladakh',
    'Puducherry',
    'Chandigarh',
    'Dadra and Nagar Haveli and Daman and Diu',
    'Lakshadweep',
    'Andaman and Nicobar Islands',
  ];

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _companyController.dispose();
    _designationController.dispose();
    _gstinController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  void _loadUserData() {
    final currentUser = ref.read(currentUserProvider);
    if (currentUser != null) {
      _nameController.text = currentUser.displayName ?? '';
      _emailController.text = currentUser.email ?? '';
      _phoneController.text = currentUser.phoneNumber ?? '';
    }

    // Default values for demo
    _companyController.text = 'All About Insurance';
    _designationController.text = 'Senior Insurance Agent';
    _selectedRole = 'Insurance Agent';
    _selectedLanguage = 'Hindi';
    _selectedState = 'Maharashtra';

    // Initialize date if available
    if (_selectedDate != null) {
      _dateController.text = '${_selectedDate!.day.toString().padLeft(2, '0')}/${_selectedDate!.month.toString().padLeft(2, '0')}/${_selectedDate!.year}';
    }
  }

  Future<void> _pickImage(bool isProfileImage) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );
      
      if (image != null) {
        setState(() {
          if (isProfileImage) {
            _profileImage = File(image.path);
          } else {
            _companyLogo = File(image.path);
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick image: $e'),
            backgroundColor: const Color(0xFFe92933),
          ),
        );
      }
    }
  }



  String? _validateDate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Date of birth is required';
    }

    if (value.length != 10) {
      return 'Please enter date in DD/MM/YYYY format';
    }

    final parts = value.split('/');
    if (parts.length != 3) {
      return 'Please enter date in DD/MM/YYYY format';
    }

    try {
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);

      if (day < 1 || day > 31) {
        return 'Invalid day. Please enter a day between 1-31';
      }

      if (month < 1 || month > 12) {
        return 'Invalid month. Please enter a month between 1-12';
      }

      if (year < 1950 || year > DateTime.now().year) {
        return 'Invalid year. Please enter a year between 1950-${DateTime.now().year}';
      }

      // Try to create the date to check if it's valid
      final date = DateTime(year, month, day);
      if (date.day != day || date.month != month || date.year != year) {
        return 'Invalid date. Please check the date';
      }

      // Check if date is not in the future
      if (date.isAfter(DateTime.now())) {
        return 'Date of birth cannot be in the future';
      }

    } catch (e) {
      return 'Invalid date format. Please use DD/MM/YYYY';
    }

    return null;
  }

  Future<void> _showImprovedDatePicker() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime(1990),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      helpText: 'Select Date of Birth', // Changed help text
      cancelText: 'Cancel',
      confirmText: 'Select',
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFFe92933), // Red primary color
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF111418),
              secondary: Color(0xFFe92933), // Red secondary color
              onSecondary: Colors.white,
            ),
            textTheme: const TextTheme(
              headlineMedium: TextStyle( // Year/Month navigation
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Color(0xFF111418),
              ),
              titleMedium: TextStyle( // Month/Year selector
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF111418),
              ),
              bodyLarge: TextStyle( // Date numbers
                fontSize: 14,
                color: Color(0xFF111418),
              ),
            ),
            iconTheme: const IconThemeData(
              color: Color(0xFF111418), // Navigation arrows
              size: 24,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = '${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}';
      });
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    // Clear focus from all text fields
    FocusScope.of(context).unfocus();

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implement actual profile save logic
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile saved successfully!'),
            backgroundColor: Color(0xFF4CAF50),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save profile: $e'),
            backgroundColor: const Color(0xFFe92933),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFf1f1f1),
      body: Column(
        children: [
          // Sticky Header
          _buildStickyHeader(),

          // Scrollable Content
          Expanded(
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),

                    // Profile & Logo Section
                    _buildCombinedImageSection(),

                    const SizedBox(height: 24),

                    // Personal Information Section
                    _buildSectionWithHeader('PERSONAL INFORMATION', _buildPersonalInformationCard()),

                    const SizedBox(height: 24),

                    // Professional Information Section
                    _buildSectionWithHeader('PROFESSIONAL INFORMATION', _buildProfessionalInformationCard()),

                    const SizedBox(height: 24),

                    // Location & Language Section
                    _buildSectionWithHeader('LOCATION & LANGUAGE', _buildLocationLanguageCard()),

                    const SizedBox(height: 32),

                    // Save Button
                    _buildSaveButton(),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStickyHeader() {
    return Container(
      color: const Color(0xFFf1f1f1),
      child: SafeArea(
        bottom: false,
        child: Container(
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: const BoxDecoration(
            color: Color(0xFFf1f1f1),
            boxShadow: [
              BoxShadow(
                color: Color(0x0F000000),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Back Button
              IconButton(
                icon: const Icon(Icons.arrow_back, color: Color(0xFFe92933)),
                onPressed: () => Navigator.pop(context),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),

              // Title
              const Expanded(
                child: Text(
                  'Profile',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color(0xFF111418),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Save Button
              ElevatedButton(
                onPressed: _isLoading ? null : _saveProfile,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFe92933),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  elevation: 0,
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Save',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionWithHeader(String title, Widget content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Title (outside card, in grey area)
        Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 12),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF111418).withValues(alpha: 0.6),
              letterSpacing: 0.5,
            ),
          ),
        ),
        // Section Content
        content,
      ],
    );
  }

  Widget _buildCombinedImageSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Color(0xFFf1f1f1), // Match screen background, no border or shadow
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Profile Picture Section
          Column(
            children: [
              Stack(
                children: [
                  Container(
                    width: 96,
                    height: 96,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white, // White background inside circle
                    ),
                    child: _profileImage != null
                        ? ClipOval(
                            child: Image.file(
                              _profileImage!,
                              width: 92, // Slightly smaller to account for border
                              height: 92,
                              fit: BoxFit.cover,
                            ),
                          )
                        : const Icon(
                            Icons.person_outline, // Changed from camera to person outline
                            size: 40,
                            color: Color(0xFF637488),
                          ),
                  ),
                  Positioned(
                    bottom: 0, // Fixed positioning to prevent cutoff
                    right: 0,
                    child: GestureDetector(
                      onTap: () => _pickImage(true),
                      child: Container(
                        width: 28, // Slightly smaller to fit better
                        height: 28,
                        decoration: const BoxDecoration(
                          color: Color(0xFFe92933),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.edit, // Changed from camera to pen icon
                          size: 14,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                'Profile Picture',
                style: TextStyle(
                  color: Color(0xFF637488),
                  fontSize: 14,
                ),
              ),
            ],
          ),

          // Company Logo Section
          Column(
            children: [
              Stack(
                children: [
                  Container(
                    width: 96,
                    height: 96,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle, // Changed from rectangular to circular
                      color: Colors.white, // White background inside circle
                    ),
                    child: _companyLogo != null
                        ? ClipOval( // Changed from ClipRRect to ClipOval
                            child: Image.file(
                              _companyLogo!,
                              width: 92, // Slightly smaller to account for border
                              height: 92,
                              fit: BoxFit.cover,
                            ),
                          )
                        : const Icon(
                            Icons.camera_alt, // Camera icon for company logo
                            size: 40,
                            color: Color(0xFF637488),
                          ),
                  ),
                  Positioned(
                    bottom: 0, // Fixed positioning to prevent cutoff
                    right: 0,
                    child: GestureDetector(
                      onTap: () => _pickImage(false),
                      child: Container(
                        width: 28, // Slightly smaller to fit better
                        height: 28,
                        decoration: const BoxDecoration(
                          color: Color(0xFFe92933), // Changed to red to match profile
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.edit, // Changed from camera to pen icon
                          size: 14,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                'Company Logo',
                style: TextStyle(
                  color: Color(0xFF637488),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }



  Widget _buildPersonalInformationCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTextField(
                  controller: _nameController,
                  label: 'Full Name',
                  placeholder: 'Enter your full name',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Full name is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _emailController,
                  label: 'Email Address',
                  placeholder: 'Enter your email',
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Email is required';
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                      return 'Please enter a valid email';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildPhoneTextField(
                  controller: _phoneController,
                  label: 'Mobile Number',
                  placeholder: '98765 43210',
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Mobile number is required';
                    }
                    if (value.length != 10) {
                      return 'Mobile number must be 10 digits';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                _buildDateField(),
              ],
            ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String placeholder,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      style: const TextStyle(
        color: Color(0xFF111418),
        fontSize: 16,
      ),
      decoration: InputDecoration(
        labelText: label, // Floating label
        hintText: placeholder,
        labelStyle: const TextStyle(
          color: Color(0xFF637488),
          fontSize: 16,
        ),
        hintStyle: const TextStyle(
          color: Color(0xFF637488),
          fontSize: 16,
        ),
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFe0e0e0)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFe0e0e0)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFe92933), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        floatingLabelStyle: const TextStyle(
          color: Color(0xFFe92933), // Red color when focused
          fontSize: 16,
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }

  Widget _buildPhoneTextField({
    required TextEditingController controller,
    required String label,
    required String placeholder,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      style: const TextStyle(
        color: Color(0xFF111418),
        fontSize: 16,
      ),
      decoration: InputDecoration(
        labelText: label, // Floating label
        prefixIcon: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: const Text(
            '+91',
            style: TextStyle(
              color: Color(0xFF111418),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        prefixIconConstraints: const BoxConstraints(minWidth: 0, minHeight: 0),
        hintText: placeholder,
        labelStyle: const TextStyle(
          color: Color(0xFF637488),
          fontSize: 16,
        ),
        hintStyle: const TextStyle(
          color: Color(0xFF637488),
          fontSize: 16,
        ),
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFe0e0e0)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFe0e0e0)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFe92933), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        floatingLabelStyle: const TextStyle(
          color: Color(0xFFe92933), // Red color when focused
          fontSize: 16,
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }

  Widget _buildDateField() {
    return GestureDetector(
      onTap: _showImprovedDatePicker,
      child: AbsorbPointer(
        child: TextFormField(
          controller: _dateController,
          validator: _validateDate,
          style: const TextStyle(
            color: Color(0xFF111418),
            fontSize: 16,
          ),
          decoration: InputDecoration(
            labelText: 'Date of Birth', // Floating label
            hintText: 'DD/MM/YYYY',
            labelStyle: const TextStyle(
              color: Color(0xFF637488),
              fontSize: 16,
            ),
                hintStyle: const TextStyle(
                  color: Color(0xFF637488),
                  fontSize: 16,
                ),
                prefixIcon: const Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: Color(0xFF637488),
                ),
                filled: true,
                fillColor: Colors.white,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFe0e0e0)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFe0e0e0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFe92933), width: 2),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.red),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.red, width: 2),
                ),
                floatingLabelStyle: const TextStyle(
                  color: Color(0xFFe92933), // Red color when focused
                  fontSize: 16,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16), // Increased padding to prevent label cutting
                errorStyle: const TextStyle(
                  height: 1.2, // Reduced height to minimize movement
                ),
              ),
            ),
          ),
        );
  }

  Widget _buildProfessionalInformationCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTextField(
                  controller: _companyController,
                  label: 'Company Name',
                  placeholder: 'Enter company name',
                ),
                const SizedBox(height: 16),
                _buildDropdownField(
                  label: 'Role',
                  value: _selectedRole,
                  items: _roles,
                  onChanged: (value) {
                    setState(() {
                      _selectedRole = value;
                    });
                  },
                  placeholder: 'Select your role',
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _designationController,
                  label: 'Designation',
                  placeholder: 'Enter your designation',
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _gstinController,
                  label: 'GSTIN',
                  placeholder: 'Enter GST identification number',
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      if (value.length != 15) {
                        return 'GSTIN must be 15 characters long';
                      }
                      // Basic GSTIN format validation
                      final gstinRegex = RegExp(r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$');
                      if (!gstinRegex.hasMatch(value)) {
                        return 'Invalid GSTIN format';
                      }
                    }
                    return null;
                  },
                ),
              ],
            ),
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
    required String placeholder,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      onChanged: onChanged,
      dropdownColor: Colors.white, // White dropdown background
      isExpanded: true, // Ensures the dropdown takes full width and prevents overflow
      decoration: InputDecoration(
        labelText: label, // Floating label
        hintText: placeholder,
        labelStyle: const TextStyle(
          color: Color(0xFF637488),
          fontSize: 16,
        ),
        hintStyle: const TextStyle(
          color: Color(0xFF637488),
          fontSize: 16,
        ),
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFe0e0e0)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFe0e0e0)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFe92933), width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        floatingLabelStyle: const TextStyle(
          color: Color(0xFFe92933), // Red color when focused
          fontSize: 16,
        ),
        // Adjusted padding to account for the dropdown icon space
        contentPadding: const EdgeInsets.only(left: 16, right: 48, top: 16, bottom: 16),
        // Add suffix icon to properly position the dropdown arrow inside the field
        suffixIcon: const Icon(
          Icons.keyboard_arrow_down,
          color: Color(0xFF637488),
          size: 24,
        ),
      ),
      // Remove the external icon since we're using suffixIcon
      icon: const SizedBox.shrink(),
      style: const TextStyle(
        color: Color(0xFF111418),
        fontSize: 16,
      ),
      items: items.map((String item) {
        return DropdownMenuItem<String>(
          value: item,
          child: Text(
            item,
            style: const TextStyle(
              color: Color(0xFF111418),
              fontSize: 16,
            ),
            overflow: TextOverflow.ellipsis, // Handle text overflow
            maxLines: 1, // Ensure single line display
          ),
        );
      }).toList(),
    );
  }

  Widget _buildLocationLanguageCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDropdownField(
                  label: 'Native Language',
                  value: _selectedLanguage,
                  items: _languages,
                  onChanged: (value) {
                    setState(() {
                      _selectedLanguage = value;
                    });
                  },
                  placeholder: 'Select your native language',
                ),
                const SizedBox(height: 16),
                _buildDropdownField(
                  label: 'State of Residence',
                  value: _selectedState,
                  items: _states,
                  onChanged: (value) {
                    setState(() {
                      _selectedState = value;
                    });
                  },
                  placeholder: 'Select your state',
                ),
              ],
            ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveProfile,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFe92933),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Save Profile',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
      ),
    );
  }
}

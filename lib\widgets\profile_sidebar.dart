import 'package:flutter/material.dart';
import '../services/navigation_service.dart';

class ProfileSidebar extends StatelessWidget {
  final VoidCallback? onClose;
  final VoidCallback? onLogout;
  final VoidCallback? onEditProfile;
  final String userName;
  final String userEmail;
  final String userPhone;
  final String? profileImageUrl;

  const ProfileSidebar({
    super.key,
    this.onClose,
    this.onLogout,
    this.onEditProfile,
    this.userName = 'Name',
    this.userEmail = '<EMAIL>',
    this.userPhone = '+91 - - - - - - - - - -',
    this.profileImageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 20,
            offset: Offset(4, 0),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Header with close button
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    onPressed: onClose ?? () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Color(0xFF637488),
                      size: 24,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shape: const CircleBorder(),
                    ),
                  ),
                ],
              ),
            ),
            
            // Profile Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  // Profile Picture with Edit Button
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 48,
                        backgroundColor: const Color(0xFFe92933),
                        backgroundImage: profileImageUrl != null 
                            ? NetworkImage(profileImageUrl!) 
                            : null,
                        child: profileImageUrl == null
                            ? const Icon(
                                Icons.person,
                                size: 48,
                                color: Colors.white,
                              )
                            : null,
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: onEditProfile,
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFF146dc9),
                              shape: BoxShape.circle,
                            ),
                            padding: const EdgeInsets.all(8),
                            child: const Icon(
                              Icons.edit,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // User Info
                  Text(
                    userName,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF111418),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    userEmail,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF637488),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    userPhone,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF637488),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Navigation Menu
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    Expanded(
                      child: ListView(
                        children: [
                          _buildMenuItem(
                            icon: Icons.edit_outlined,
                            title: 'Edit Profile',
                            onTap: onEditProfile ?? () {},
                          ),
                          _buildMenuItem(
                            icon: Icons.access_time,
                            title: 'Subscription Plan',
                            onTap: () {
                              Navigator.pop(context); // Close drawer
                              NavigationService.instance.navigateToSubscription();
                            },
                          ),
                          _buildMenuItem(
                            icon: Icons.link,
                            title: 'Referrals',
                            onTap: () {
                              Navigator.pop(context); // Close drawer
                              NavigationService.instance.navigateToReferral();
                            },
                          ),
                          _buildMenuItem(
                            icon: Icons.settings,
                            title: 'Settings',
                            onTap: () {
                              Navigator.pop(context); // Close drawer
                              NavigationService.instance.navigateToSettings();
                            },
                          ),
                          _buildMenuItem(
                            icon: Icons.help_outline,
                            title: 'FAQs',
                            onTap: () {
                              Navigator.pop(context); // Close drawer
                              NavigationService.instance.navigateToFAQ();
                            },
                          ),
                          _buildMenuItem(
                            icon: Icons.chat_bubble_outline,
                            title: 'Feedback',
                            onTap: () {
                              Navigator.pop(context); // Close drawer
                              NavigationService.instance.navigateToFeedback();
                            },
                          ),
                        ],
                      ),
                    ),
                    
                    // Logout Section
                    Container(
                      decoration: const BoxDecoration(
                        border: Border(
                          top: BorderSide(
                            color: Color(0xFFE5E7EB),
                            width: 1,
                          ),
                        ),
                      ),
                      padding: const EdgeInsets.only(top: 16),
                      child: _buildMenuItem(
                        icon: Icons.logout,
                        title: 'Logout',
                        isLogout: true,
                        onTap: onLogout ?? () {},
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isLogout = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: 24,
                  color: isLogout ? Colors.red : const Color(0xFF637488),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isLogout ? Colors.red : const Color(0xFF111418),
                    ),
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  size: 20,
                  color: isLogout ? Colors.red : const Color(0xFF637488),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

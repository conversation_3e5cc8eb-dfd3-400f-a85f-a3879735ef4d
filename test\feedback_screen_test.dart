import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aai/screens/feedback_screen.dart';

void main() {
  group('FeedbackScreen Tests', () {
    testWidgets('FeedbackScreen displays correctly', (WidgetTester tester) async {
      // Build the FeedbackScreen widget
      await tester.pumpWidget(
        const MaterialApp(
          home: FeedbackScreen(),
        ),
      );

      // Verify that the screen displays the main elements
      expect(find.text('Share Feedback'), findsOneWidget);
      expect(find.text('Help us improve by sharing your thoughts'), findsOneWidget);
      expect(find.text('How would you rate your experience?'), findsOneWidget);
      expect(find.text('What\'s this about?'), findsOneWidget);
      expect(find.text('Tell us more'), findsOneWidget);
      expect(find.text('Submit Feedback'), findsOneWidget);
      
      // Verify rating stars are displayed
      expect(find.byIcon(Icons.star), findsNWidgets(5));
      
      // Verify category options
      expect(find.text('App Experience'), findsOneWidget);
      expect(find.text('Feature Request'), findsOneWidget);
      expect(find.text('Bug Report'), findsOneWidget);
      expect(find.text('Performance'), findsOneWidget);
      expect(find.text('Design/UI'), findsOneWidget);
      expect(find.text('Other'), findsOneWidget);
      
      // Verify submit button exists
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('Rating selection works', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FeedbackScreen(),
        ),
      );

      // Initially no rating selected
      expect(find.text('Please select a rating'), findsOneWidget);

      // Tap on the third star
      final stars = find.byIcon(Icons.star);
      await tester.tap(stars.at(2));
      await tester.pump();

      // Verify rating is updated
      expect(find.text('Rating: 3/5'), findsOneWidget);
    });

    testWidgets('Category selection works', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FeedbackScreen(),
        ),
      );

      // Find and tap on App Experience category
      await tester.tap(find.text('App Experience'));
      await tester.pump();

      // Verify category is still displayed (tap doesn't cause errors)
      expect(find.text('App Experience'), findsOneWidget);
    });

    testWidgets('Text input works and character count updates', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FeedbackScreen(),
        ),
      );

      // Find the text field and enter text
      final textField = find.byType(TextField);
      await tester.enterText(textField, 'This is my feedback');
      await tester.pump();

      // Verify character count is updated
      expect(find.text('18/500'), findsOneWidget);
    });

    testWidgets('Form validation works', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FeedbackScreen(),
        ),
      );

      // Select rating
      final stars = find.byIcon(Icons.star);
      await tester.tap(stars.at(3));
      await tester.pump();

      // Select category
      await tester.tap(find.text('Bug Report'));
      await tester.pump();

      // Enter text
      await tester.enterText(find.byType(TextField), 'Found a bug in the app');
      await tester.pump();

      // Verify form elements are present
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('Bug Report'), findsOneWidget);
      expect(find.text('Found a bug in the app'), findsOneWidget);
    });

    testWidgets('Submit feedback works', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FeedbackScreen(),
        ),
      );

      // Fill out the form completely
      // Select rating
      final stars = find.byIcon(Icons.star);
      await tester.tap(stars.at(4));
      await tester.pump();

      // Select category
      await tester.tap(find.text('Feature Request'));
      await tester.pump();

      // Enter feedback text
      await tester.enterText(find.byType(TextField), 'Please add dark mode');
      await tester.pump();

      // Submit the form
      await tester.tap(find.text('Submit Feedback'));
      await tester.pump();

      // Verify success message appears
      expect(find.text('Thank you for your feedback!'), findsOneWidget);
    });

    testWidgets('Back button works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const FeedbackScreen()),
                ),
                child: const Text('Go to Feedback'),
              ),
            ),
          ),
        ),
      );

      // Navigate to feedback screen
      await tester.tap(find.text('Go to Feedback'));
      await tester.pumpAndSettle();

      // Verify we're on the feedback screen
      expect(find.text('Share Feedback'), findsOneWidget);

      // Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Verify we're back to the original screen
      expect(find.text('Go to Feedback'), findsOneWidget);
    });
  });
}

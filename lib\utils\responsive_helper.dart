import 'package:flutter/material.dart';

class ResponsiveHelper {
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  static const double desktopBreakpoint = 1440;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }

  static double getImageSize(BuildContext context) {
    final size = MediaQuery.of(context).size;
    if (isMobile(context)) {
      // Limit image size to prevent overflow on small screens
      final maxImageSize = size.height * 0.35; // Max 35% of screen height
      final widthBasedSize = size.width * 0.6; // Reduced from 0.7 to 0.6
      return widthBasedSize < maxImageSize ? widthBasedSize : maxImageSize;
    } else if (isTablet(context)) {
      return size.width * 0.4; // Reduced from 0.5 to 0.4
    } else {
      return 350; // Reduced from 400 to 350
    }
  }

  static EdgeInsets getHorizontalPadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.symmetric(horizontal: 16);
    } else if (isTablet(context)) {
      return const EdgeInsets.symmetric(horizontal: 48);
    } else {
      return const EdgeInsets.symmetric(horizontal: 80);
    }
  }

  static double getMaxContentWidth(BuildContext context) {
    if (isMobile(context)) {
      return double.infinity;
    } else if (isTablet(context)) {
      return 600;
    } else {
      return 800;
    }
  }

  static TextStyle? getResponsiveTextStyle(
    BuildContext context,
    TextStyle? baseStyle,
  ) {
    if (baseStyle == null) return null;
    
    final scaleFactor = _getTextScaleFactor(context);
    return baseStyle.copyWith(
      fontSize: (baseStyle.fontSize ?? 14) * scaleFactor,
    );
  }

  static double _getTextScaleFactor(BuildContext context) {
    if (isMobile(context)) {
      return 1.0;
    } else if (isTablet(context)) {
      return 1.1;
    } else {
      return 1.2;
    }
  }

  static double getVerticalSpacing(BuildContext context, double baseSpacing) {
    if (isMobile(context)) {
      return baseSpacing;
    } else if (isTablet(context)) {
      return baseSpacing * 1.2;
    } else {
      return baseSpacing * 1.5;
    }
  }

  static bool shouldUseHorizontalLayout(BuildContext context) {
    return isDesktop(context) || 
           (isTablet(context) && 
            MediaQuery.of(context).orientation == Orientation.landscape);
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/auth_state.dart';
import '../../providers/auth_provider.dart';
import '../../services/navigation_service.dart';

class AlternateRegisterScreen extends ConsumerStatefulWidget {
  const AlternateRegisterScreen({super.key});

  @override
  ConsumerState<AlternateRegisterScreen> createState() => _AlternateRegisterScreenState();
}

class _AlternateRegisterScreenState extends ConsumerState<AlternateRegisterScreen> {

  void _onGoogleSignUpPressed() {
    ref.read(authProvider.notifier).signInWithGoogle();
  }

  void _onAppleSignUpPressed() {
    // Apple sign up functionality - placeholder for now
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Apple Sign Up - Coming Soon'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _onEmailSignUpPressed() {
    NavigationService.instance.navigateToAlternateEmailSignup();
  }

  void _onLoginPressed() {
    NavigationService.instance.navigateToAlternateSignIn();
  }

  void _onTermsPressed() {
    NavigationService.instance.navigateToTerms();
  }

  void _onPrivacyPressed() {
    NavigationService.instance.navigateToPolicy();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Listen to auth state changes
    ref.listen<AuthState>(authProvider, (previous, next) {
      print('DEBUG: Alternate Register - Auth state changed from ${previous?.status} to ${next.status}');
      if (next.status == AuthStatus.error) {
        // Don't show toast for Google sign-in cancellation
        final errorMessage = next.errorMessage ?? 'An error occurred';
        print('DEBUG: Alternate Register - Error message received: "$errorMessage"');

        // Check for various cancellation messages
        final isCancellation = errorMessage.toLowerCase().contains('cancelled') ||
                              errorMessage.toLowerCase().contains('canceled') ||
                              errorMessage.toLowerCase().contains('sign in was cancelled') ||
                              errorMessage.toLowerCase().contains('sign-in was cancelled') ||
                              errorMessage.toLowerCase().contains('user cancelled') ||
                              errorMessage.toLowerCase().contains('user canceled') ||
                              errorMessage.toLowerCase().contains('google sign in failed');

        if (!isCancellation) {
          print('DEBUG: Showing toast for error: "$errorMessage"');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Center(
                child: Text(
                  errorMessage,
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        } else {
          print('DEBUG: Suppressing toast for cancellation: "$errorMessage"');
        }
      } else if (next.status == AuthStatus.authenticated) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          NavigationService.instance.navigateToHome();
        });
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Main Content - Centered
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  // Main Content
                  Expanded(
                child: Center(
                  child: SingleChildScrollView(
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(maxWidth: 400),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Welcome Text
                          _buildWelcomeText(),

                          const SizedBox(height: 40),

                          // Sign Up Buttons
                          _buildSignUpButtons(authState),
                          
                          const SizedBox(height: 32),
                          
                          // Login Link
                          _buildLoginLink(),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

                  // Footer
                  _buildFooter(),
                ],
              ),
            ),

            // Header - Positioned at top
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: _buildHeader(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          IconButton(
            onPressed: () => NavigationService.instance.goBack(),
            icon: const Icon(
              Icons.arrow_back,
              color: Color(0xFFe92933),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeText() {
    return Column(
      children: [
        const Text(
          'Welcome to',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color(0xFF1a1a1a),
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'All About Insurance',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color(0xFFe92933),
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpButtons(AuthState authState) {
    return Column(
      children: [
        // Google Button
        _buildSignUpButton(
          text: 'Sign up with Google',
          iconWidget: SizedBox(
            width: 24,
            height: 24,
            child: Image.asset(
              'assets/logo/google-logo.webp',
              width: 24,
              height: 24,
              fit: BoxFit.contain,
            ),
          ),
          onPressed: _onGoogleSignUpPressed,
          isLoading: authState.isLoading,
        ),

        const SizedBox(height: 16),

        // Apple Button
        _buildSignUpButton(
          text: 'Sign up with Apple',
          iconWidget: const Icon(
            Icons.apple,
            color: Color(0xFF1a1a1a),
            size: 24,
          ),
          onPressed: _onAppleSignUpPressed,
          isLoading: false,
        ),

        const SizedBox(height: 16),

        // Email Button
        _buildSignUpButton(
          text: 'Sign up with Email',
          iconWidget: const Icon(
            Icons.email_outlined,
            color: Color(0xFF1a1a1a),
            size: 24,
          ),
          onPressed: _onEmailSignUpPressed,
          isLoading: false,
        ),
      ],
    );
  }

  Widget _buildSignUpButton({
    required String text,
    required Widget iconWidget,
    required VoidCallback onPressed,
    required bool isLoading,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFf2f2f2),
          foregroundColor: const Color(0xFF1a1a1a),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            iconWidget,
            const SizedBox(width: 12),
            Stack(
              alignment: Alignment.center,
              children: [
                Text(
                  text,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1a1a1a),
                  ),
                ),
                if (isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFe92933)),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginLink() {
    return RichText(
      text: TextSpan(
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF666666),
        ),
        children: [
          const TextSpan(text: "Already have an account? "),
          TextSpan(
            text: 'Login',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFFe92933),
            ),
            recognizer: TapGestureRecognizer()..onTap = _onLoginPressed,
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF666666),
          ),
          children: [
            const TextSpan(text: "By signing up, you agree to our "),
            TextSpan(
              text: 'Terms of Service',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Color(0xFFe92933),
              ),
              recognizer: TapGestureRecognizer()..onTap = _onTermsPressed,
            ),
            const TextSpan(text: " and "),
            TextSpan(
              text: 'Privacy Policy',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Color(0xFFe92933),
              ),
              recognizer: TapGestureRecognizer()..onTap = _onPrivacyPressed,
            ),
            const TextSpan(text: "."),
          ],
        ),
      ),
    );
  }
}

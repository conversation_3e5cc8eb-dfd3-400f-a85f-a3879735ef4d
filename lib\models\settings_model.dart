class SettingsModel {
  final bool pushNotificationsEnabled;
  final bool emailNotificationsEnabled;
  final bool locationServicesEnabled;
  final bool analyticsEnabled;
  final bool darkModeEnabled;
  final bool biometricEnabled;
  final bool autoDownloadEnabled;

  const SettingsModel({
    this.pushNotificationsEnabled = true,
    this.emailNotificationsEnabled = false,
    this.locationServicesEnabled = true,
    this.analyticsEnabled = false,
    this.darkModeEnabled = false,
    this.biometricEnabled = true,
    this.autoDownloadEnabled = false,
  });

  SettingsModel copyWith({
    bool? pushNotificationsEnabled,
    bool? emailNotificationsEnabled,
    bool? locationServicesEnabled,
    bool? analyticsEnabled,
    bool? darkModeEnabled,
    bool? biometricEnabled,
    bool? autoDownloadEnabled,
  }) {
    return SettingsModel(
      pushNotificationsEnabled: pushNotificationsEnabled ?? this.pushNotificationsEnabled,
      emailNotificationsEnabled: emailNotificationsEnabled ?? this.emailNotificationsEnabled,
      locationServicesEnabled: locationServicesEnabled ?? this.locationServicesEnabled,
      analyticsEnabled: analyticsEnabled ?? this.analyticsEnabled,
      darkModeEnabled: darkModeEnabled ?? this.darkModeEnabled,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      autoDownloadEnabled: autoDownloadEnabled ?? this.autoDownloadEnabled,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pushNotificationsEnabled': pushNotificationsEnabled,
      'emailNotificationsEnabled': emailNotificationsEnabled,
      'locationServicesEnabled': locationServicesEnabled,
      'analyticsEnabled': analyticsEnabled,
      'darkModeEnabled': darkModeEnabled,
      'biometricEnabled': biometricEnabled,
      'autoDownloadEnabled': autoDownloadEnabled,
    };
  }

  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      pushNotificationsEnabled: json['pushNotificationsEnabled'] ?? true,
      emailNotificationsEnabled: json['emailNotificationsEnabled'] ?? false,
      locationServicesEnabled: json['locationServicesEnabled'] ?? true,
      analyticsEnabled: json['analyticsEnabled'] ?? false,
      darkModeEnabled: json['darkModeEnabled'] ?? false,
      biometricEnabled: json['biometricEnabled'] ?? true,
      autoDownloadEnabled: json['autoDownloadEnabled'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is SettingsModel &&
      other.pushNotificationsEnabled == pushNotificationsEnabled &&
      other.emailNotificationsEnabled == emailNotificationsEnabled &&
      other.locationServicesEnabled == locationServicesEnabled &&
      other.analyticsEnabled == analyticsEnabled &&
      other.darkModeEnabled == darkModeEnabled &&
      other.biometricEnabled == biometricEnabled &&
      other.autoDownloadEnabled == autoDownloadEnabled;
  }

  @override
  int get hashCode {
    return pushNotificationsEnabled.hashCode ^
      emailNotificationsEnabled.hashCode ^
      locationServicesEnabled.hashCode ^
      analyticsEnabled.hashCode ^
      darkModeEnabled.hashCode ^
      biometricEnabled.hashCode ^
      autoDownloadEnabled.hashCode;
  }

  @override
  String toString() {
    return 'SettingsModel(pushNotificationsEnabled: $pushNotificationsEnabled, emailNotificationsEnabled: $emailNotificationsEnabled, locationServicesEnabled: $locationServicesEnabled, analyticsEnabled: $analyticsEnabled, darkModeEnabled: $darkModeEnabled, biometricEnabled: $biometricEnabled, autoDownloadEnabled: $autoDownloadEnabled)';
  }
}

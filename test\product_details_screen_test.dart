import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aai/screens/products/product_details_screen.dart';

void main() {
  group('ProductDetailsScreen Tests', () {
    testWidgets('should display product details correctly', (WidgetTester tester) async {
      // Arrange
      const productName = 'Health Insurance';
      const productDescription = 'Comprehensive health coverage';
      const companyName = 'SecureHealth';
      const companyId = 'securehealth';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: const ProductDetailsScreen(
            productName: productName,
            productDescription: productDescription,
            companyName: companyName,
            companyId: companyId,
          ),
        ),
      );

      // Assert - Product name appears in header and product details, so expect at least one
      expect(find.text(productName), findsAtLeastNWidgets(1));
      expect(find.text(companyName), findsOneWidget);
      expect(find.text('Benefits'), findsOneWidget);
      expect(find.text('Premium'), findsOneWidget);
      expect(find.text('Documents'), findsOneWidget);

      // Check for edit icons
      expect(find.byIcon(Icons.edit), findsNWidgets(4)); // One for each field

      // Check for action buttons (may need scrolling in real app)
      expect(find.text('Get Quote'), findsOneWidget);
      expect(find.text('Compare'), findsOneWidget);
      expect(find.text('Share PDF'), findsOneWidget);
      expect(find.text('Contact Agent'), findsOneWidget);
    });

    testWidgets('should show get quote dialog when button is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const ProductDetailsScreen(
            productName: 'Health Insurance',
            productDescription: 'Comprehensive health coverage',
            companyName: 'SecureHealth',
            companyId: 'securehealth',
          ),
        ),
      );

      // Act
      await tester.tap(find.text('Get Quote'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Get Quote'), findsAtLeastNWidgets(1)); // At least one (could be in dialog)
      expect(find.text('Get a personalized quote for Health Insurance from SecureHealth.'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets('should show compare dialog when button is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const ProductDetailsScreen(
            productName: 'Health Insurance',
            productDescription: 'Comprehensive health coverage',
            companyName: 'SecureHealth',
            companyId: 'securehealth',
          ),
        ),
      );

      // Act
      await tester.tap(find.text('Compare'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Compare Plans'), findsAtLeastNWidgets(1)); // At least one (could be in dialog)
      expect(find.text('Compare this plan with other similar products to find the best option for you.'), findsOneWidget);
      expect(find.text('Compare'), findsAtLeastNWidgets(1));
    });

    testWidgets('should show contact dialog when button is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const ProductDetailsScreen(
            productName: 'Health Insurance',
            productDescription: 'Comprehensive health coverage',
            companyName: 'SecureHealth',
            companyId: 'securehealth',
          ),
        ),
      );

      // Act
      await tester.tap(find.text('Contact Agent'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Contact Agent'), findsAtLeastNWidgets(1)); // At least one (could be in dialog)
      expect(find.text('Connect with our insurance expert to get personalized advice and assistance.'), findsOneWidget);
      expect(find.text('Contact'), findsOneWidget);
    });

    testWidgets('should navigate back when back button is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ProductDetailsScreen(
                        productName: 'Health Insurance',
                        productDescription: 'Comprehensive health coverage',
                        companyName: 'SecureHealth',
                        companyId: 'securehealth',
                      ),
                    ),
                  );
                },
                child: const Text('Navigate'),
              ),
            ),
          ),
        ),
      );

      // Act - Navigate to product details
      await tester.tap(find.text('Navigate'));
      await tester.pumpAndSettle();

      // Verify we're on the product details screen
      expect(find.text('Health Insurance'), findsAtLeastNWidgets(1));

      // Act - Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Assert - Should be back to the original screen
      expect(find.text('Navigate'), findsOneWidget);
      expect(find.text('Health Insurance'), findsNothing);
    });

    testWidgets('should display different benefits for different product types', (WidgetTester tester) async {
      // Test health insurance benefits
      await tester.pumpWidget(
        MaterialApp(
          home: const ProductDetailsScreen(
            productName: 'Health Insurance',
            productDescription: 'Comprehensive health coverage',
            companyName: 'SecureHealth',
            companyId: 'securehealth',
          ),
        ),
      );

      expect(find.text('Hospitalization'), findsOneWidget);
      expect(find.text('Outpatient Care'), findsOneWidget);
      expect(find.text('Prescription Drugs'), findsOneWidget);
      expect(find.text('Emergency Care'), findsOneWidget);

      // Test motor insurance benefits
      await tester.pumpWidget(
        MaterialApp(
          home: const ProductDetailsScreen(
            productName: 'Car Insurance',
            productDescription: 'Comprehensive car coverage',
            companyName: 'AutoProtect',
            companyId: 'autoprotect',
          ),
        ),
      );

      expect(find.text('Own Damage'), findsOneWidget);
      expect(find.text('Third Party'), findsOneWidget);
      expect(find.text('Personal Accident'), findsOneWidget);
    });

    testWidgets('should show edit dialog when edit icon is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const ProductDetailsScreen(
            productName: 'Health Insurance',
            productDescription: 'Comprehensive health coverage',
            companyName: 'SecureHealth',
            companyId: 'securehealth',
          ),
        ),
      );

      // Find the first edit icon (Company field)
      final editIcons = find.byIcon(Icons.edit);
      expect(editIcons, findsNWidgets(4));

      // Act - Tap the first edit icon
      await tester.tap(editIcons.first);
      await tester.pumpAndSettle();

      // Assert - Edit dialog should appear
      expect(find.text('Edit Company'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Next'), findsOneWidget);
    });
  });
}

import 'package:flutter/material.dart';

class FAQScreen extends StatefulWidget {
  const FAQScreen({super.key});

  @override
  State<FAQScreen> createState() => _FAQScreenState();
}

class _FAQScreenState extends State<FAQScreen> {
  int? _expandedIndex; // Track which FAQ is currently expanded

  final List<Map<String, String>> faqData = [
    {
      'question': 'How do I get started?',
      'answer': 'Getting started is easy! Simply download our app, create an account, and follow the onboarding process. You\'ll be guided through setting up your profile and preferences to get the most out of our service.',
    },
    {
      'question': 'Is there a mobile app available?',
      'answer': 'Yes! Our mobile app is available for both iOS and Android devices. You can download it from the App Store or Google Play Store. The mobile app offers all the features of our web platform with a mobile-optimized experience.',
    },
    {
      'question': 'How do I contact customer support?',
      'answer': 'You can reach our customer support team through multiple channels: email <NAME_EMAIL>, use the in-app chat feature, or call our helpline during business hours. We typically respond to inquiries within 24 hours.',
    },
    {
      'question': 'What are your pricing plans?',
      'answer': 'We offer flexible pricing plans to suit different needs. Our basic plan is free with limited features, while our premium plans start at \$9.99/month. Visit our pricing page for detailed information about features included in each plan.',
    },
    {
      'question': 'Is my data secure?',
      'answer': 'Absolutely! We take data security very seriously. All data is encrypted in transit and at rest using industry-standard encryption. We comply with GDPR and other privacy regulations to ensure your information is protected.',
    },
    {
      'question': 'Can I cancel my subscription anytime?',
      'answer': 'Yes, you can cancel your subscription at any time from your account settings. There are no cancellation fees, and you\'ll continue to have access to premium features until the end of your current billing period.',
    },
    {
      'question': 'Do you offer refunds?',
      'answer': 'We offer a 30-day money-back guarantee for all new subscriptions. If you\'re not satisfied with our service within the first 30 days, contact our support team for a full refund.',
    },
    {
      'question': 'How do I update my account information?',
      'answer': 'You can update your account information by going to Settings > Profile in the app or website. From there, you can change your email, password, personal details, and notification preferences.',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF1F1F1),
      body: Column(
        children: [
          // Sticky Header
          _buildStickyHeader(),

          // Scrollable Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const SizedBox(height: 8),

                  // Question mark icon
                  _buildIconSection(),
                  const SizedBox(height: 32),

                  // FAQ List
                  _buildFAQList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStickyHeader() {
    return Container(
      color: const Color(0xFFf1f1f1),
      child: SafeArea(
        bottom: false,
        child: Container(
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: const BoxDecoration(
            color: Color(0xFFf1f1f1),
            boxShadow: [
              BoxShadow(
                color: Color(0x0F000000),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Back Button
              IconButton(
                icon: const Icon(Icons.arrow_back, color: Color(0xFFe92933)),
                onPressed: () => Navigator.pop(context),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),

              // Title
              const Expanded(
                child: Text(
                  'FAQs',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color(0xFF111418),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Spacer to balance the layout
              const SizedBox(width: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIconSection() {
    return Container(
      width: 64,
      height: 64,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.red, Color(0xFFE91E63)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(32),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: const Icon(
        Icons.help_outline,
        color: Colors.white,
        size: 32,
      ),
    );
  }



  Widget _buildFAQList() {
    return Container(
      key: ValueKey('faq_list_$_expandedIndex'), // Force rebuild when expansion changes
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: faqData.asMap().entries.map((entry) {
          final index = entry.key;
          final faq = entry.value;
          final isLast = index == faqData.length - 1;

          return _buildFAQItem(
            index,
            index + 1,
            faq['question']!,
            faq['answer']!,
            isLast,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildFAQItem(int index, int number, String question, String answer, bool isLast) {
    final isExpanded = _expandedIndex == index;

    return Container(
      decoration: BoxDecoration(
        border: isLast ? null : Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Question tile
          InkWell(
            onTap: () {
              setState(() {
                if (isExpanded) {
                  _expandedIndex = null; // Close if already expanded
                } else {
                  _expandedIndex = index; // Open this one (closes others automatically)
                }
              });
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Text(
                        number.toString().padLeft(2, '0'),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.red,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      question,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: isExpanded ? Colors.red : Colors.grey[600],
                  ),
                ],
              ),
            ),
          ),

          // Answer section (animated)
          AnimatedCrossFade(
            duration: const Duration(milliseconds: 300),
            crossFadeState: isExpanded
                ? CrossFadeState.showSecond
                : CrossFadeState.showFirst,
            firstChild: const SizedBox.shrink(),
            secondChild: Padding(
              padding: const EdgeInsets.only(
                left: 56, // Align with question text
                right: 20,
                bottom: 16,
              ),
              child: Text(
                answer,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  height: 1.5,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

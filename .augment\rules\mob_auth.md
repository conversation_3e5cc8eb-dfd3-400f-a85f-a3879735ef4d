
# 📄 Business Requirement Document (BRD)

## **Mobile Number Authentication using Firebase in Flutter App (Android & iOS)**

---

### **1. Overview**

This document outlines the business and technical requirements for implementing **mobile number-based authentication** using **Firebase Authentication** in a cross-platform Flutter app. The feature will be available for **both Android and iOS** users.

---

### **2. Objectives**

* Enable secure, cross-platform mobile authentication using Firebase.
* Simplify login and reduce user friction by using OTP-based sign-in.
* Ensure robust error handling and compatibility across Android and iOS.
* Lay foundation for scalable user identity management using Firebase.

---

### **3. Functional Requirements**

#### **3.1 Mobile Number Entry Screen**

* screen name- alternate_signin_screen.dart
* **Send OTP Button**:

  * Sends OTP using Firebase's `verifyPhoneNumber` method.

#### **3.2 OTP Verification Screen**

* **OTP Input Field**: Accepts 6-digit OTP (either segmented or single field).
* **Countdown Timer**: Resend OTP becomes active after 30–60 seconds.
* **Verify Button**: Submits <PERSON>TP for Firebase verification.
* **Resend OTP Button**: Initiates <PERSON>TP resend after timer ends.

---

### **4. Authentication Flow**

#### **4.1 Send OTP**

* Use `FirebaseAuth.instance.verifyPhoneNumber` with platform-specific handling:

  * Android: Supports automatic OTP read via Google Play Services.
  * iOS: Requires manual OTP entry.
* Handles callbacks:

  * `codeSent`, `verificationCompleted`, `verificationFailed`, `codeAutoRetrievalTimeout`.

#### **4.2 Verify OTP**

* Use `PhoneAuthProvider.credential(verificationId, smsCode)` to create credential.
* Authenticate using `FirebaseAuth.instance.signInWithCredential`.
* On success, user is signed in and redirected to the home/dashboard.

---

### **5. Cross-Platform Considerations**

| Feature                 | Android                        | iOS                       |
| ----------------------- | ------------------------------ | ------------------------- |
| Auto OTP detection      | ✅ (if Play Services available) | ❌ (manual input required) |
| Firebase Phone Auth     | ✅                              | ✅                         |
| Resend OTP via Firebase | ✅                              | ✅                         |
| Background SMS read     | ✅ (with permissions)           | ❌ (not supported)         |

---

### **6. Edge Case Handling**

* OTP expiry timeout.
* Handling invalid or expired OTPs.
* Handling network delays or Firebase errors.
* Limiting resend attempts to prevent abuse.
* Showing contextual error messages to user.

---

### **7. Non-Functional Requirements**

* **Security**: Use Firebase’s secure backend to protect against SMS spoofing or reuse.
* **Scalability**: Capable of supporting thousands of concurrent users.
* **Performance**: Authentication flow should complete within 5-6 seconds.

---

### **8. Technical Requirements**

* **Flutter SDK**: Latest stable version.
* **Firebase Setup**:

  * **Android**:

    * `google-services.json` added.
    * SHA-1 and SHA-256 keys configured.
  * **iOS**:

    * `GoogleService-Info.plist` added.
    * Apple Developer account provisioning, Push Notifications, and APNs setup.
* **Dependencies**:

  * `firebase_core`
  * `firebase_auth`
  * `fluttertoast`, `intl_phone_number_input` (optional UX tools)
* **State Management**: Any (Provider, Riverpod, BLoC, etc.)

---

### **9. Future Enhancements**

* **"Remember Me"** toggle to persist login session.
* **Referral Code Entry** during or after login.
* Integration with **Firestore** to store additional user data.
* **Multi-Factor Authentication (MFA)**.
* **Biometric Login** after first OTP-based login.

---

### **10. Out of Scope**

* Email/password or social logins.
* Admin-side phone verification or OTP dispatch control.
* App web version (if applicable).


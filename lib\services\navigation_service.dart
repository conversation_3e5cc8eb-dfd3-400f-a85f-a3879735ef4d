import 'package:flutter/material.dart';
import '../utils/app_routes.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static NavigationService get instance => _instance;

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  BuildContext? get context => navigatorKey.currentContext;

  // Navigate to a route with optional arguments
  Future<T?> navigateTo<T>(String routeName, {Object? arguments}) async {
    if (context == null) {
      debugPrint('NavigationService: Context is null, cannot navigate to $routeName');
      return null;
    }
    
    try {
      return await Navigator.of(context!).pushNamed<T>(routeName, arguments: arguments);
    } catch (e) {
      debugPrint('NavigationService: Error navigating to $routeName: $e');
      // Fallback to sign-in screen on navigation error
      return await _fallbackToSignIn<T>();
    }
  }

  // Navigate and remove all previous routes
  Future<T?> navigateAndClearStack<T>(String routeName, {Object? arguments}) async {
    if (context == null) {
      debugPrint('NavigationService: Context is null, cannot navigate to $routeName');
      return null;
    }
    
    try {
      return await Navigator.of(context!).pushNamedAndRemoveUntil<T>(
        routeName,
        (route) => false,
        arguments: arguments,
      );
    } catch (e) {
      debugPrint('NavigationService: Error navigating and clearing stack to $routeName: $e');
      return await _fallbackToSignIn<T>();
    }
  }

  // Replace current route
  Future<T?> navigateAndReplace<T>(String routeName, {Object? arguments}) async {
    if (context == null) {
      debugPrint('NavigationService: Context is null, cannot replace with $routeName');
      return null;
    }
    
    try {
      return await Navigator.of(context!).pushReplacementNamed<T, Object?>(
        routeName,
        arguments: arguments,
      );
    } catch (e) {
      debugPrint('NavigationService: Error replacing with $routeName: $e');
      return await _fallbackToSignIn<T>();
    }
  }

  // Go back
  void goBack<T>([T? result]) {
    if (context == null) {
      debugPrint('NavigationService: Context is null, cannot go back');
      return;
    }
    
    try {
      if (Navigator.of(context!).canPop()) {
        Navigator.of(context!).pop<T>(result);
      } else {
        // If can't go back, fallback to sign-in
        _fallbackToSignIn();
      }
    } catch (e) {
      debugPrint('NavigationService: Error going back: $e');
      _fallbackToSignIn();
    }
  }

  // Specific navigation methods following the flow rules
  void navigateToOnboarding() => navigateAndReplace(AppRoutes.onboarding);
  void navigateToAlternateSignIn() => navigateAndReplace(AppRoutes.alternateSignIn);
  Future<T?> navigateToOtp<T>({required String phoneNumber, String? verificationId}) {
    return navigateTo<T>(AppRoutes.otp, arguments: {
      'phoneNumber': phoneNumber,
      'verificationId': verificationId,
    });
  }
  void navigateToAlternateLogin() => navigateTo(AppRoutes.alternateLogin);
  void navigateToAlternateRegister() => navigateTo(AppRoutes.alternateRegister);
  void navigateToAlternateForgotPassword() => navigateTo(AppRoutes.alternateForgotPassword);
  void navigateToAlternateEmailSignup() => navigateTo(AppRoutes.alternateEmailSignup);
  void navigateToAlternateEmailVerification({required String email, required String password}) {
    navigateTo(AppRoutes.alternateEmailVerification, arguments: {'email': email, 'password': password});
  }
  void navigateToPhoneAuth() => navigateTo(AppRoutes.phoneAuth);
  void navigateToHome() => navigateAndClearStack(AppRoutes.alternateHome);
  void navigateToAlternateHome() => navigateAndClearStack(AppRoutes.alternateHome);
  void navigateToProfile() => navigateTo(AppRoutes.profile);
  void navigateToEnhancedProfile() => navigateTo(AppRoutes.enhancedProfile);
  void navigateToNotifications() => navigateTo(AppRoutes.notifications);
  void navigateToSettings() => navigateTo(AppRoutes.settings);
  void navigateToSubscription() => navigateTo(AppRoutes.subscription);
  void navigateToTerms() => navigateTo(AppRoutes.terms);
  void navigateToReferral() => navigateTo(AppRoutes.referral);
  void navigateToFeedback() => navigateTo(AppRoutes.feedback);
  void navigateToFAQ() => navigateTo(AppRoutes.faq);
  void navigateToPolicy() => navigateTo(AppRoutes.policy);

  // Fallback method for error handling
  Future<T?> _fallbackToSignIn<T>() async {
    debugPrint('NavigationService: Falling back to alternate sign-in screen');
    try {
      return await Navigator.of(context!).pushNamedAndRemoveUntil<T>(
        AppRoutes.alternateSignIn,
        (route) => false,
      );
    } catch (e) {
      debugPrint('NavigationService: Critical error - cannot even fallback to alternate sign-in: $e');
      return null;
    }
  }

  // Check if we can go back
  bool canGoBack() {
    if (context == null) return false;
    return Navigator.of(context!).canPop();
  }
}

1.	Splash screen
a.	Navigates to onboarding for first-time users
i.	Page indicator dots at the bottom (using SmoothPageIndicator)
ii.	"Next" button at the bottom (changes to "Get Started" on last page)
iii.	“Skip" button in the top-right (except on last page) and Get started btn
1.	User completes onboarding → _completeOnboarding() → widget.onComplete() → _navigateToAuth() → Navigate to sign-in screen
a.	Sign up btn in header
i.	Register screen
1.	Login btn in header-
a.	Sign_in_screen
2.	login link
a.	Sign_in_screen
3.	Terms text
a.	Back btn
i.	Sign_in_screen
4.	policy text
a.	Back btn
i.	Sign_in_screen
5.	Continue with Google
a.	Google account selection popup
i.	Home screen logout   sign_in_screen
6.	Continue with email
a.	Email signup screen
i.	Back btnregister screen
ii.	Signup btn FirebaseEmailVerificationScreen
iii.	Resend email btnNo navigation
iv.	Listener for auth state changes to detect when email is verified
v.	Automatic navigation to home screen when verification is complete
vi.	LogoutSign_in_screen
b.	Sign up link
i.	Register screen
c.	Terms text
i.	Back btn
1.	Sign_in_screen
d.	policy text
i.	Back btn
1.	Sign_in_screen
e.	Continue with Google
i.	Google account selection popup
1.	Home screen
a.	Logout
i.	Sign_in_screen
f.	Continue with email
i.	Login screen
1.	Back arrow
a.	Sign_in_screen
2.	Submit Email & password
a.	Home screen
i.	Logout  Sign_in_screen
3.	Forgot password link
a.	Forgot password screen
i.	Email sent successfully sets _emailSent = true, which changes the UI to show a success message: 
ii.	Back button Sign_in_screen
b.	Navigates to sign_in_Screen for returning users




1.	Splash Screen to Onboarding 
o	Occurs for first-time users and asp reinstall users
2.	Splash Screen to Sign-in Screen 
o	Occurs for returning users
3.	Onboarding to Onboarding 
o	Internal navigation between onboarding pages using next btn or swipe gesture
4.	Onboarding to Sign-in Screen 
o	Occurs when user completes or skips onboarding
5.	Sign-in Screen to Register Screen 
o	Occurs when user taps "Sign up" button in header or "Sign up" link
6.	Sign-in Screen to Terms/Policy and back 
o	Opens Terms or Policy screen and returns via back button
o	Implemented with standard navigation and back button behavior
7.	Sign-in Screen to Google Account Selection 
o	Occurs when user taps "Continue with Google"
o	Opens system-level Google account picker dialog
8.	Google Account Selection to Home Screen [direct]
o	Occurs after successful Google authentication
9.	Sign-in Screen to Login Screen 
o	Occurs when user taps "Continue with Email" in login context
10.	Login Screen to Sign-in Screen 
o	Occurs when user taps back arrow
11.	Login Screen to Home Screen 
o	Occurs after successful login
o	Triggred by login btn press
12.	Login Screen to Forgot Password Screen
o	Occurs when user taps "Forgot password" link
13.	Forgot Password Screen to itself 
o	Submits email and "Email sent successfully" changes UI but doesn't navigate
o	Stays on same screen with success message
14.	Forgot Password Screen to Sign-in Screen 
o	Occurs when user taps "Back to Login" button
15.	Register Screen to Sign-in Screen
o	Occurs when user taps "Login" button in header or "login" link
16.	Register Screen to Google Account Selection 
o	Occurs when user taps "Continue with Google"
o	Opens system-level Google account picker dialog 
17.	Google Account Selection to Home Screen [when coming from register screen)
o	Occurs after successful Google authentication
18.	Register screen to email sign up screen 
o	Occurs when user taps "Continue with Email" in sign up context
19.	Email Signup Screen to sign in Screen 
o	Occurs when user taps back button
20.	Email Signup Screen to Firebase Email Verification 
o	Occurs after successful signup upon clicking sign up btn
21.	Firebase Email Verification  Screen to itself 
o	"Resend email" doesn't navigate, just triggers action
o	Stays on same screen
22.	Firebase Email Verification to Home Screen 
o	Occurs when email is verified
23.	Home Screen to Sign-in Screen
o	Occurs when user logs out
24.	Sign in screen to google account selection after user logs out
o	Occurs after successful Google authentication
o	Automatic navigation to home page after successful auth

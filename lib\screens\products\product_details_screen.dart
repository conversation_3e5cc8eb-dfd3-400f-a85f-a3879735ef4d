import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/navigation_service.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/profile_sidebar.dart';

enum EditType { company, product, plan }

class ProductDetailsScreen extends ConsumerStatefulWidget {
  final String productName;
  final String productDescription;
  final String companyName;
  final String companyId;

  const ProductDetailsScreen({
    super.key,
    required this.productName,
    required this.productDescription,
    required this.companyName,
    required this.companyId,
  });

  @override
  ConsumerState<ProductDetailsScreen> createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends ConsumerState<ProductDetailsScreen> {
  int _selectedTabIndex = 0;
  late ScrollController _tabScrollController;

  // Current selected values
  late String _currentCompany;
  late String _currentProduct;
  late String _currentPlan;
  late String _currentSumInsured;

  // Data structure for hierarchical relationships
  final Map<String, Map<String, dynamic>> _insuranceData = {
    'SecureHealth': {
      'id': 'securehealth',
      'products': {
        'Health Insurance': {
          'plans': {
            'Basic Plan': ['₹5,00,000', '₹10,00,000', '₹15,00,000'],
            'Premium Plan': ['₹25,00,000', '₹50,00,000', '₹75,00,000'],
            'Family Plan': ['₹10,00,000', '₹20,00,000', '₹30,00,000'],
          }
        },
        'Family Plans': {
          'plans': {
            'Standard Family': ['₹15,00,000', '₹25,00,000', '₹35,00,000'],
            'Premium Family': ['₹40,00,000', '₹60,00,000', '₹80,00,000'],
          }
        },
        'Senior Citizen': {
          'plans': {
            'Senior Basic': ['₹3,00,000', '₹5,00,000', '₹8,00,000'],
            'Senior Premium': ['₹10,00,000', '₹15,00,000', '₹20,00,000'],
          }
        },
      }
    },
    'LifeGuard': {
      'id': 'lifeguard',
      'products': {
        'Term Life': {
          'plans': {
            'Basic Term': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
            'Premium Term': ['₹75,00,000', '₹1,50,00,000', '₹2,00,00,000'],
          }
        },
        'Whole Life': {
          'plans': {
            'Standard Whole': ['₹20,00,000', '₹40,00,000', '₹60,00,000'],
            'Premium Whole': ['₹50,00,000', '₹1,00,00,000', '₹1,50,00,000'],
          }
        },
        'ULIP Plans': {
          'plans': {
            'Growth ULIP': ['₹15,00,000', '₹30,00,000', '₹50,00,000'],
            'Balanced ULIP': ['₹20,00,000', '₹40,00,000', '₹75,00,000'],
          }
        },
      }
    },
    'AutoProtect': {
      'id': 'autoprotect',
      'products': {
        'Car Insurance': {
          'plans': {
            'Third Party': ['₹5,00,000', '₹10,00,000', '₹15,00,000'],
            'Comprehensive': ['₹10,00,000', '₹20,00,000', '₹30,00,000'],
          }
        },
        'Bike Insurance': {
          'plans': {
            'Basic Bike': ['₹2,00,000', '₹3,00,000', '₹5,00,000'],
            'Premium Bike': ['₹5,00,000', '₹8,00,000', '₹10,00,000'],
          }
        },
      }
    },
    'TravelSafe': {
      'id': 'travelsafe',
      'products': {
        'Domestic Travel': {
          'plans': {
            'Basic Domestic': ['₹1,00,000', '₹2,00,000', '₹3,00,000'],
            'Premium Domestic': ['₹3,00,000', '₹5,00,000', '₹7,00,000'],
          }
        },
        'International': {
          'plans': {
            'Basic International': ['₹5,00,000', '₹10,00,000', '₹15,00,000'],
            'Premium International': ['₹15,00,000', '₹25,00,000', '₹50,00,000'],
          }
        },
      }
    },
  };

  @override
  void initState() {
    super.initState();
    _tabScrollController = ScrollController();
    _currentCompany = widget.companyName;
    _currentProduct = widget.productName;
    _currentPlan = 'Premium Plan'; // Default plan
    _currentSumInsured = '₹50,00,000'; // Default sum insured
  }

  @override
  void dispose() {
    _tabScrollController.dispose();
    super.dispose();
  }

  void _onTabChanged(int index) {
    setState(() {
      _selectedTabIndex = index;
    });

    // Auto-scroll to bring selected tab into view
    _scrollToTab(index);
  }

  void _scrollToTab(int index) {
    if (!_tabScrollController.hasClients) return;

    // Calculate the position of the selected tab
    const double tabWidth = 80.0; // Fixed width for each tab
    const double tabMargin = 8.0; // 4px margin on each side
    const double leftPadding = 16.0; // Left padding of the scroll view

    // Calculate the total width per tab (width + margins)
    const double totalTabWidth = tabWidth + tabMargin;

    // Calculate the position of the selected tab
    final double targetPosition = leftPadding + (index * totalTabWidth);

    // Get the viewport width
    final double viewportWidth = _tabScrollController.position.viewportDimension;

    // Calculate the scroll position to center the tab in the viewport
    final double scrollPosition = targetPosition - (viewportWidth / 2) + (tabWidth / 2);

    // Clamp the scroll position to valid bounds
    final double maxScrollExtent = _tabScrollController.position.maxScrollExtent;
    final double clampedPosition = scrollPosition.clamp(0.0, maxScrollExtent);

    // Animate to the calculated position
    _tabScrollController.animateTo(
      clampedPosition,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFf1f1f1),
      drawer: Drawer(
        child: Consumer(
          builder: (context, ref, child) {
            final currentUser = ref.watch(currentUserProvider);
            return ProfileSidebar(
              userName: currentUser?.displayName ?? 'Name',
              userEmail: currentUser?.email ?? '<EMAIL>',
              userPhone: currentUser?.phoneNumber ?? '+91 - - - - - - - - - -',
              profileImageUrl: currentUser?.photoURL,
              onLogout: () {
                Navigator.pop(context); // Close drawer
                ref.read(authProvider.notifier).signOut();
                NavigationService.instance.navigateToAlternateSignIn();
              },
              onEditProfile: () {
                Navigator.pop(context); // Close drawer
                NavigationService.instance.navigateToProfile();
              },
            );
          },
        ),
      ),
      body: Column(
        children: [
          // Custom header with shadow
          Container(
            color: Colors.white,
            child: SafeArea(
              bottom: false,
              child: Container(
                height: 56,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x0F000000),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Color(0xFFe92933)),
                      onPressed: () => Navigator.pop(context),
                    ),
                    Expanded(
                      child: Text(
                        widget.productName,
                        style: const TextStyle(
                          color: Color(0xFF111418),
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    // Burger menu
                    IconButton(
                      onPressed: () {
                        Scaffold.of(context).openDrawer();
                      },
                      icon: const Icon(
                        Icons.menu,
                        color: Color(0xFFe92933),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Body content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Details Card
                  _buildProductDetailsCard(),
                  const SizedBox(height: 16),

                  // Tabs and Content Section
                  _buildTabsAndContentSection(),
                  const SizedBox(height: 16),

                  // Action Buttons (Compare and Share PDF only)
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductDetailsCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                // 2x2 Grid of details without edit icons
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem('Company', _currentCompany),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildDetailItem('Product', _currentProduct),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem('Plan', _currentPlan),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildDetailItem('Sum Insured', _currentSumInsured),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
          // Single edit button positioned at top-right corner
          Positioned(
            top: 8,
            right: 8,
            child: IconButton(
              onPressed: () => _showEditDialog(),
              icon: const Icon(
                Icons.edit,
                color: Color(0xFFe92933),
                size: 20,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF637488),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF111418),
          ),
        ),
      ],
    );
  }

  Widget _buildEditableDetailItem(String label, String value, VoidCallback onEdit) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF637488),
              ),
            ),
            const SizedBox(width: 4),
            GestureDetector(
              onTap: onEdit,
              child: const Icon(
                Icons.edit,
                color: Color(0xFFe92933),
                size: 16,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF111418),
          ),
        ),
      ],
    );
  }

  Widget _buildTabsAndContentSection() {
    return Column(
      children: [
        // Tabs Section - matching home page design exactly (edge-to-edge)
        Column(
          children: [
            SingleChildScrollView(
              controller: _tabScrollController,
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  const SizedBox(width: 16), // Left padding
                  _buildTab('Benefits', 0),
                  _buildTab('Premium', 1),
                  _buildTab('Documents', 2),
                  _buildTab('Links', 3),
                  const SizedBox(width: 16), // Right padding
                ],
              ),
            ),
            // Running line below all tabs (edge-to-edge)
            Container(
              height: 1,
              width: double.infinity,
              color: const Color(0xFFE0E0E0),
            ),
          ],
        ),
        // Tab Content with standard padding
        Container(
          width: double.infinity,
          color: const Color(0xFFf1f1f1),
          padding: const EdgeInsets.all(16),
          child: _buildTabContent(),
        ),
      ],
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedTabIndex == index;
    // Dynamic width based on text length to prevent overflow
    double tabWidth = 80; // Default width
    if (title == 'Documents') {
      tabWidth = 100; // Wider for "Documents" to prevent text wrapping
    } else if (title == 'Premium') {
      tabWidth = 85; // Slightly wider for "Premium"
    }

    return GestureDetector(
      onTap: () => _onTabChanged(index),
      child: Container(
        width: tabWidth,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFe92933) : null,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
            bottomLeft: Radius.circular(0),
            bottomRight: Radius.circular(0),
          ),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: isSelected ? Colors.white : const Color(0xFF086788),
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedTabIndex) {
      case 0:
        return _buildBenefitsContent();
      case 1:
        return _buildPremiumContent();
      case 2:
        return _buildDocumentsContent();
      case 3:
        return _buildLinksContent();
      default:
        return _buildBenefitsContent();
    }
  }

  Widget _buildBenefitsContent() {
    return _buildBenefitTable();
  }

  Widget _buildPremiumContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Premium Details',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        _buildPremiumRow('Annual Premium', '₹15,000'),
        const SizedBox(height: 8),
        _buildPremiumRow('Monthly Premium', '₹1,250'),
        const SizedBox(height: 8),
        _buildPremiumRow('Processing Fee', '₹500'),
        const SizedBox(height: 8),
        _buildPremiumRow('GST (18%)', '₹2,790'),
        const Divider(),
        _buildPremiumRow('Total Amount', '₹18,290', isTotal: true),
      ],
    );
  }

  Widget _buildPremiumRow(String label, String amount, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            color: const Color(0xFF111418),
          ),
        ),
        Text(
          amount,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: isTotal ? const Color(0xFFe92933) : const Color(0xFF111418),
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentsContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Required Documents',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        _buildDocumentItem('Policy Document', 'Download policy terms and conditions'),
        _buildDocumentItem('Proposal Form', 'Application form for this policy'),
        _buildDocumentItem('Premium Receipt', 'Payment confirmation receipt'),
        _buildDocumentItem('Claim Form', 'Form to submit insurance claims'),
      ],
    );
  }

  Widget _buildDocumentItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          const Icon(
            Icons.description,
            color: Color(0xFFe92933),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF111418),
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF637488),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Downloading $title...'),
                  backgroundColor: const Color(0xFFe92933),
                ),
              );
            },
            icon: const Icon(
              Icons.download,
              color: Color(0xFFe92933),
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLinksContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Useful Links',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        _buildLinkItem('Policy Terms & Conditions', 'Read complete policy terms', Icons.description),
        _buildLinkItem('Claim Process Guide', 'Step-by-step claim filing guide', Icons.help_outline),
        _buildLinkItem('Customer Support', 'Contact our support team', Icons.support_agent),
        _buildLinkItem('Branch Locator', 'Find nearest branch office', Icons.location_on),
        _buildLinkItem('Online Portal', 'Access your policy online', Icons.web),
        _buildLinkItem('Mobile App', 'Download our mobile app', Icons.phone_android),
      ],
    );
  }

  Widget _buildLinkItem(String title, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            color: const Color(0xFFe92933),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF111418),
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF637488),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Opening $title...'),
                  backgroundColor: const Color(0xFFe92933),
                ),
              );
            },
            icon: const Icon(
              Icons.open_in_new,
              color: Color(0xFFe92933),
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitTable() {
    final benefits = _getBenefitsForProduct();

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFe0e0e0), width: 1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(2),
          1: FlexColumnWidth(2),
          2: FlexColumnWidth(1.5),
        },
        border: TableBorder(
          horizontalInside: const BorderSide(color: Color(0xFFe0e0e0), width: 1),
          verticalInside: const BorderSide(color: Color(0xFFe0e0e0), width: 1),
        ),
        children: [
          // Header row
          TableRow(
            decoration: const BoxDecoration(
              color: Color(0xFFf8f9fa),
            ),
            children: [
              _buildTableHeader('Benefit Type'),
              _buildTableHeader('Coverage'),
              _buildTableHeader('Limit'),
            ],
          ),
          // Data rows
          ...benefits.map((benefit) => TableRow(
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            children: [
              _buildTableCell(benefit['type']!, benefit['description']!),
              _buildTableCellBadge(benefit['coverage']!),
              _buildTableCellAmount(benefit['limit']!),
            ],
          )),
        ],
      ),
    );
  }

  Widget _buildTableHeader(String text) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: Color(0xFF637488),
        ),
      ),
    );
  }

  Widget _buildTableCell(String title, String subtitle) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Color(0xFF111418),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 10,
              color: Color(0xFF637488),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableCellBadge(String text) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: const Color(0xFFf0f0f0),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFe0e0e0)),
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Color(0xFF111418),
          ),
        ),
      ),
    );
  }

  Widget _buildTableCellAmount(String amount) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Text(
        amount,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: Color(0xFF111418),
        ),
        textAlign: TextAlign.right,
      ),
    );
  }

  List<Map<String, String>> _getBenefitsForProduct() {
    // Return different benefits based on product type
    if (widget.productName.toLowerCase().contains('health') ||
        widget.productName.toLowerCase().contains('medical')) {
      return [
        {
          'type': 'Hospitalization',
          'description': 'Room and board, ICU, surgery',
          'coverage': '100% after deductible',
          'limit': '₹5,00,000',
        },
        {
          'type': 'Outpatient Care',
          'description': 'Doctor visits, consultations',
          'coverage': '80% after copay',
          'limit': '₹50,000',
        },
        {
          'type': 'Prescription Drugs',
          'description': 'Generic and brand medications',
          'coverage': '70% after copay',
          'limit': '₹25,000',
        },
        {
          'type': 'Emergency Care',
          'description': 'Emergency room, ambulance',
          'coverage': '100% covered',
          'limit': '₹2,00,000',
        },
      ];
    } else if (widget.productName.toLowerCase().contains('motor') ||
               widget.productName.toLowerCase().contains('car') ||
               widget.productName.toLowerCase().contains('bike')) {
      return [
        {
          'type': 'Own Damage',
          'description': 'Vehicle damage coverage',
          'coverage': '100% IDV',
          'limit': 'As per IDV',
        },
        {
          'type': 'Third Party',
          'description': 'Legal liability coverage',
          'coverage': 'Unlimited',
          'limit': 'Unlimited',
        },
        {
          'type': 'Personal Accident',
          'description': 'Driver/owner coverage',
          'coverage': '100% covered',
          'limit': '₹15,00,000',
        },
      ];
    } else if (widget.productName.toLowerCase().contains('life')) {
      return [
        {
          'type': 'Death Benefit',
          'description': 'Sum assured on death',
          'coverage': '100% sum assured',
          'limit': '₹1,00,00,000',
        },
        {
          'type': 'Maturity Benefit',
          'description': 'Survival benefit',
          'coverage': 'As per policy',
          'limit': '₹50,00,000',
        },
        {
          'type': 'Accidental Death',
          'description': 'Additional coverage',
          'coverage': '200% sum assured',
          'limit': '₹2,00,00,000',
        },
      ];
    } else {
      return [
        {
          'type': 'Basic Coverage',
          'description': 'Standard protection',
          'coverage': '100% covered',
          'limit': '₹10,00,000',
        },
        {
          'type': 'Additional Benefits',
          'description': 'Extended coverage',
          'coverage': '80% covered',
          'limit': '₹5,00,000',
        },
      ];
    }
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              _showCompareDialog();
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: const Color(0xFFe92933),
              side: const BorderSide(color: Color(0xFFe92933)),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Compare',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              _shareProductPDF();
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: const Color(0xFFe92933),
              side: const BorderSide(color: Color(0xFFe92933)),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Share PDF',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _shareProductPDF() async {
    try {
      // Create a comprehensive product details text for sharing
      final productDetails = '''
${widget.productName} - Product Details

Company: $_currentCompany
Product: $_currentProduct
Plan: $_currentPlan
Sum Insured: $_currentSumInsured

Benefits Overview:
${_getBenefitsText()}

Premium Information:
${_getPremiumText()}

Documents Required:
${_getDocumentsText()}

Important Links:
${_getLinksText()}

Generated by All About Insurance App
''';

      // Share the product details
      await Share.share(
        productDetails,
        subject: '${widget.productName} - Insurance Product Details',
      );
    } catch (e) {
      // Show error message if sharing fails
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to share product details. Please try again.'),
            backgroundColor: Color(0xFFe92933),
          ),
        );
      }
    }
  }

  String _getBenefitsText() {
    final benefits = _getBenefitsForProduct();
    return benefits.map((benefit) =>
      '• ${benefit['type']}: ${benefit['coverage']} - ${benefit['limit']}'
    ).join('\n');
  }

  String _getPremiumText() {
    return '''
• Monthly Premium: ₹2,500
• Annual Premium: ₹28,000 (Save ₹2,000)
• Tax Benefits: Up to ₹1.5 Lakh under 80C
• Claim Settlement Ratio: 98.5%''';
  }

  String _getDocumentsText() {
    return '''
• Identity Proof (Aadhaar/PAN/Passport)
• Address Proof (Utility Bill/Bank Statement)
• Income Proof (Salary Slip/ITR)
• Medical Reports (if applicable)
• Photographs (Passport size)''';
  }

  String _getLinksText() {
    return '''
• Official Website: www.${_currentCompany.toLowerCase()}.com
• Customer Care: 1800-XXX-XXXX
• Claim Portal: claims.${_currentCompany.toLowerCase()}.com
• Policy Documents: docs.${_currentCompany.toLowerCase()}.com''';
  }

  void _showGetQuoteDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            'Get Quote',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
          ),
          content: Text(
            'Get a personalized quote for ${widget.productName} from ${widget.companyName}.',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF637488),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Color(0xFF637488)),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // TODO: Navigate to quote form
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Quote request submitted!'),
                    backgroundColor: Color(0xFFe92933),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
              ),
              child: const Text('Get Quote'),
            ),
          ],
        );
      },
    );
  }

  void _showCompareDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            'Compare Plans',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
          ),
          content: const Text(
            'Compare this plan with other similar products to find the best option for you.',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF637488),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Color(0xFF637488)),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // TODO: Navigate to comparison screen
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Opening comparison tool...'),
                    backgroundColor: Color(0xFFe92933),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
              ),
              child: const Text('Compare'),
            ),
          ],
        );
      },
    );
  }

  void _showContactDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            'Contact Agent',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
          ),
          content: const Text(
            'Connect with our insurance expert to get personalized advice and assistance.',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF637488),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Color(0xFF637488)),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // TODO: Navigate to contact screen or initiate call
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Connecting you with an agent...'),
                    backgroundColor: Color(0xFFe92933),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
              ),
              child: const Text('Contact'),
            ),
          ],
        );
      },
    );
  }

  // Edit methods for each field
  void _editCompany() {
    _showMultiStepEditDialog(
      title: 'Edit Company',
      editType: EditType.company,
      currentCompany: _currentCompany,
      currentProduct: _currentProduct,
      currentPlan: _currentPlan,
      currentSumInsured: _currentSumInsured,
    );
  }

  void _editProduct() {
    _showMultiStepEditDialog(
      title: 'Edit Product',
      editType: EditType.product,
      currentCompany: _currentCompany,
      currentProduct: _currentProduct,
      currentPlan: _currentPlan,
      currentSumInsured: _currentSumInsured,
    );
  }

  void _editPlan() {
    _showMultiStepEditDialog(
      title: 'Edit Plan',
      editType: EditType.plan,
      currentCompany: _currentCompany,
      currentProduct: _currentProduct,
      currentPlan: _currentPlan,
      currentSumInsured: _currentSumInsured,
    );
  }

  void _editSumInsured() {
    _showSumInsuredEditDialog();
  }

  void _showSumInsuredEditDialog() {
    final availableSumInsured = _getAvailableSumInsured(_currentCompany, _currentProduct, _currentPlan);
    String selectedSumInsured = _currentSumInsured;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              title: const Text(
                'Edit Sum Insured',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF111418),
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Locked fields
                  _buildLockedField('Company', _currentCompany),
                  _buildLockedField('Product', _currentProduct),
                  _buildLockedField('Plan', _currentPlan),
                  const SizedBox(height: 16),
                  const Text(
                    'Sum Insured',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF111418),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Sum Insured options
                  ...availableSumInsured.map((amount) => RadioListTile<String>(
                    title: Text(amount),
                    value: amount,
                    groupValue: selectedSumInsured,
                    activeColor: const Color(0xFFe92933),
                    onChanged: (value) {
                      setState(() {
                        selectedSumInsured = value!;
                      });
                    },
                  )),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(color: Color(0xFF637488)),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    setState(() {
                      _currentSumInsured = selectedSumInsured;
                    });
                    _showSuccessMessage('Sum Insured updated successfully!');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFe92933),
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Save'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildLockedField(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            Icons.lock,
            size: 16,
            color: Colors.grey[400],
          ),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF637488),
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Color(0xFF111418),
            ),
          ),
        ],
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // Helper methods for data filtering
  List<String> _getAvailableCompanies() {
    return _insuranceData.keys.toList();
  }

  List<String> _getAvailableProducts(String company) {
    return _insuranceData[company]?['products']?.keys.toList() ?? [];
  }

  List<String> _getAvailablePlans(String company, String product) {
    return _insuranceData[company]?['products']?[product]?['plans']?.keys.toList() ?? [];
  }

  List<String> _getAvailableSumInsured(String company, String product, String plan) {
    return _insuranceData[company]?['products']?[product]?['plans']?[plan] ?? [];
  }

  void _showMultiStepEditDialog({
    required String title,
    required EditType editType,
    required String currentCompany,
    required String currentProduct,
    required String currentPlan,
    required String currentSumInsured,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return _MultiStepEditDialog(
          title: title,
          editType: editType,
          currentCompany: currentCompany,
          currentProduct: currentProduct,
          currentPlan: currentPlan,
          currentSumInsured: currentSumInsured,
          insuranceData: _insuranceData,
          onSave: (company, product, plan, sumInsured) {
            setState(() {
              _currentCompany = company;
              _currentProduct = product;
              _currentPlan = plan;
              _currentSumInsured = sumInsured;
            });
            _showSuccessMessage('Details updated successfully!');
          },
        );
      },
    );
  }

  void _showShareDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            'Share Product Details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
          ),
          content: Text(
            'Share ${widget.productName} details as PDF with your customers.',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF637488),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Color(0xFF637488)),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // TODO: Generate and share PDF
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Generating PDF for sharing...'),
                    backgroundColor: Color(0xFFe92933),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
              ),
              child: const Text('Share PDF'),
            ),
          ],
        );
      },
    );
  }

  void _showEditDialog() {
    showDialog(
      context: context,
      builder: (context) => EditProductDialog(
        currentCompany: _currentCompany,
        currentProduct: _currentProduct,
        currentPlan: _currentPlan,
        currentSumInsured: _currentSumInsured,
        insuranceData: _insuranceData,
        onUpdate: (company, product, plan, sumInsured) {
          setState(() {
            _currentCompany = company;
            _currentProduct = product;
            _currentPlan = plan;
            _currentSumInsured = sumInsured;
          });
        },
      ),
    );
  }
}

class EditProductDialog extends StatefulWidget {
  final String currentCompany;
  final String currentProduct;
  final String currentPlan;
  final String currentSumInsured;
  final Map<String, Map<String, dynamic>> insuranceData;
  final Function(String, String, String, String) onUpdate;

  const EditProductDialog({
    super.key,
    required this.currentCompany,
    required this.currentProduct,
    required this.currentPlan,
    required this.currentSumInsured,
    required this.insuranceData,
    required this.onUpdate,
  });

  @override
  State<EditProductDialog> createState() => _EditProductDialogState();
}

class _EditProductDialogState extends State<EditProductDialog> {
  late PageController _pageController;
  int _currentStep = 0;
  final int _totalSteps = 4;
  bool _isLoading = false;

  // Current selections
  late String _selectedCompany;
  late String _selectedProduct;
  late String _selectedPlan;
  late String _selectedSumInsured;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // Initialize with current values
    _selectedCompany = widget.currentCompany;
    _selectedProduct = widget.currentProduct;
    _selectedPlan = widget.currentPlan;
    _selectedSumInsured = widget.currentSumInsured;
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      title: const Text(
        'Edit Product Details',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Color(0xFF111418),
        ),
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: Column(
          children: [
            // Step indicator
            _buildStepIndicator(),
            const SizedBox(height: 16),
            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : PageView(
                      controller: _pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: _buildStepPages(),
                    ),
            ),
          ],
        ),
      ),
      actions: [
        if (_currentStep > 0)
          TextButton(
            onPressed: _goToPreviousStep,
            child: const Text('Back'),
          ),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _currentStep == _totalSteps - 1 ? _saveChanges : _goToNextStep,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFe92933),
            foregroundColor: Colors.white,
          ),
          child: Text(_currentStep == _totalSteps - 1 ? 'Save' : 'Next'),
        ),
      ],
    );
  }

  Widget _buildStepIndicator() {
    return Row(
      children: List.generate(_totalSteps, (index) {
        final isActive = index <= _currentStep;
        final isCompleted = index < _currentStep;

        return Expanded(
          child: Container(
            height: 4,
            margin: EdgeInsets.only(right: index < _totalSteps - 1 ? 8 : 0),
            decoration: BoxDecoration(
              color: isActive ? const Color(0xFFe92933) : const Color(0xFFe0e0e0),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        );
      }),
    );
  }

  List<Widget> _buildStepPages() {
    return [
      _buildCompanySelectionPage(),
      _buildProductSelectionPage(),
      _buildPlanSelectionPage(),
      _buildSumInsuredSelectionPage(),
    ];
  }

  Widget _buildCompanySelectionPage() {
    final companies = widget.insuranceData.keys.toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Company',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: companies.length,
            itemBuilder: (context, index) {
              final company = companies[index];
              return RadioListTile<String>(
                title: Text(company),
                value: company,
                groupValue: _selectedCompany,
                activeColor: const Color(0xFFe92933),
                onChanged: (value) {
                  setState(() {
                    _selectedCompany = value!;
                    // Reset dependent selections when company changes
                    final products = widget.insuranceData[_selectedCompany]?['products']?.keys.toList() ?? [];
                    if (products.isNotEmpty) {
                      _selectedProduct = products.first;
                      final plans = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?.keys.toList() ?? [];
                      if (plans.isNotEmpty) {
                        _selectedPlan = plans.first;
                        final sumInsuredOptions = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?[_selectedPlan] ?? [];
                        if (sumInsuredOptions.isNotEmpty) {
                          _selectedSumInsured = sumInsuredOptions.first;
                        }
                      }
                    }
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductSelectionPage() {
    final products = widget.insuranceData[_selectedCompany]?['products']?.keys.toList() ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLockedFieldInDialog('Company', _selectedCompany),
        const SizedBox(height: 16),
        const Text(
          'Select Product',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              return RadioListTile<String>(
                title: Text(product),
                value: product,
                groupValue: _selectedProduct,
                activeColor: const Color(0xFFe92933),
                onChanged: (value) {
                  setState(() {
                    _selectedProduct = value!;
                    // Reset dependent selections when product changes
                    final plans = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?.keys.toList() ?? [];
                    if (plans.isNotEmpty) {
                      _selectedPlan = plans.first;
                      final sumInsuredOptions = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?[_selectedPlan] ?? [];
                      if (sumInsuredOptions.isNotEmpty) {
                        _selectedSumInsured = sumInsuredOptions.first;
                      }
                    }
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPlanSelectionPage() {
    final plans = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?.keys.toList() ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLockedFieldInDialog('Company', _selectedCompany),
        _buildLockedFieldInDialog('Product', _selectedProduct),
        const SizedBox(height: 16),
        const Text(
          'Select Plan',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: plans.length,
            itemBuilder: (context, index) {
              final plan = plans[index];
              return RadioListTile<String>(
                title: Text(plan),
                value: plan,
                groupValue: _selectedPlan,
                activeColor: const Color(0xFFe92933),
                onChanged: (value) {
                  setState(() {
                    _selectedPlan = value!;
                    // Reset dependent selections when plan changes
                    final sumInsuredOptions = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?[_selectedPlan] ?? [];
                    if (sumInsuredOptions.isNotEmpty) {
                      _selectedSumInsured = sumInsuredOptions.first;
                    }
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSumInsuredSelectionPage() {
    final sumInsuredOptions = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?[_selectedPlan] ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLockedFieldInDialog('Company', _selectedCompany),
        _buildLockedFieldInDialog('Product', _selectedProduct),
        _buildLockedFieldInDialog('Plan', _selectedPlan),
        const SizedBox(height: 16),
        const Text(
          'Select Sum Insured',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: sumInsuredOptions.length,
            itemBuilder: (context, index) {
              final sumInsured = sumInsuredOptions[index];
              return RadioListTile<String>(
                title: Text(sumInsured),
                value: sumInsured,
                groupValue: _selectedSumInsured,
                activeColor: const Color(0xFFe92933),
                onChanged: (value) {
                  setState(() {
                    _selectedSumInsured = value!;
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLockedFieldInDialog(String label, String value) {
    return Row(
      children: [
        Icon(
          Icons.lock,
          size: 16,
          color: Colors.grey[400],
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF637488),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Color(0xFF111418),
          ),
        ),
      ],
    );
  }

  void _goToNextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _saveChanges() {
    widget.onUpdate(_selectedCompany, _selectedProduct, _selectedPlan, _selectedSumInsured);
    Navigator.pop(context);
  }
}

class _MultiStepEditDialog extends StatefulWidget {
  final String title;
  final EditType editType;
  final String currentCompany;
  final String currentProduct;
  final String currentPlan;
  final String currentSumInsured;
  final Map<String, Map<String, dynamic>> insuranceData;
  final Function(String, String, String, String) onSave;

  const _MultiStepEditDialog({
    required this.title,
    required this.editType,
    required this.currentCompany,
    required this.currentProduct,
    required this.currentPlan,
    required this.currentSumInsured,
    required this.insuranceData,
    required this.onSave,
  });

  @override
  State<_MultiStepEditDialog> createState() => _MultiStepEditDialogState();
}

class _MultiStepEditDialogState extends State<_MultiStepEditDialog> {
  late PageController _pageController;
  int _currentStep = 0;
  late String _selectedCompany;
  late String _selectedProduct;
  late String _selectedPlan;
  late String _selectedSumInsured;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _selectedCompany = widget.currentCompany;
    _selectedProduct = widget.currentProduct;
    _selectedPlan = widget.currentPlan;
    _selectedSumInsured = widget.currentSumInsured;

    // Set initial step based on edit type
    switch (widget.editType) {
      case EditType.company:
        _currentStep = 0;
        break;
      case EditType.product:
        _currentStep = 1;
        break;
      case EditType.plan:
        _currentStep = 2;
        break;
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  int get _totalSteps {
    switch (widget.editType) {
      case EditType.company:
        return 4;
      case EditType.product:
        return 3;
      case EditType.plan:
        return 2;
    }
  }

  List<String> _getStepTitles() {
    switch (widget.editType) {
      case EditType.company:
        return ['Company', 'Product', 'Plan', 'Sum Insured'];
      case EditType.product:
        return ['Product', 'Plan', 'Sum Insured'];
      case EditType.plan:
        return ['Plan', 'Sum Insured'];
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      title: Text(
        widget.title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Color(0xFF111418),
        ),
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: Column(
          children: [
            // Step indicator
            _buildStepIndicator(),
            const SizedBox(height: 16),
            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : PageView(
                      controller: _pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: _buildStepPages(),
                    ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text(
            'Cancel',
            style: TextStyle(color: Color(0xFF637488)),
          ),
        ),
        if (_currentStep > _getStartStep())
          TextButton(
            onPressed: _goToPreviousStep,
            child: const Text(
              'Back',
              style: TextStyle(color: Color(0xFFe92933)),
            ),
          ),
        ElevatedButton(
          onPressed: _currentStep == _totalSteps - 1 ? _saveChanges : _goToNextStep,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFe92933),
            foregroundColor: Colors.white,
          ),
          child: Text(_currentStep == _totalSteps - 1 ? 'Save' : 'Next'),
        ),
      ],
    );
  }

  int _getStartStep() {
    switch (widget.editType) {
      case EditType.company:
        return 0;
      case EditType.product:
        return 1;
      case EditType.plan:
        return 2;
    }
  }

  Widget _buildStepIndicator() {
    final stepTitles = _getStepTitles();
    final startStep = _getStartStep();

    return Row(
      children: List.generate(stepTitles.length, (index) {
        final actualStep = startStep + index;
        final isActive = actualStep == _currentStep;
        final isCompleted = actualStep < _currentStep;

        return Expanded(
          child: Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: isActive || isCompleted
                            ? const Color(0xFFe92933)
                            : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: isCompleted
                            ? const Icon(Icons.check, color: Colors.white, size: 16)
                            : Text(
                                '${index + 1}',
                                style: TextStyle(
                                  color: isActive ? Colors.white : Colors.grey[600],
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      stepTitles[index],
                      style: TextStyle(
                        fontSize: 10,
                        color: isActive ? const Color(0xFFe92933) : Colors.grey[600],
                        fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              if (index < stepTitles.length - 1)
                Container(
                  width: 20,
                  height: 2,
                  color: isCompleted ? const Color(0xFFe92933) : Colors.grey[300],
                ),
            ],
          ),
        );
      }),
    );
  }

  List<Widget> _buildStepPages() {
    final pages = <Widget>[];

    switch (widget.editType) {
      case EditType.company:
        pages.addAll([
          _buildCompanySelectionPage(),
          _buildProductSelectionPage(),
          _buildPlanSelectionPage(),
          _buildSumInsuredSelectionPage(),
        ]);
        break;
      case EditType.product:
        pages.addAll([
          _buildProductSelectionPage(),
          _buildPlanSelectionPage(),
          _buildSumInsuredSelectionPage(),
        ]);
        break;
      case EditType.plan:
        pages.addAll([
          _buildPlanSelectionPage(),
          _buildSumInsuredSelectionPage(),
        ]);
        break;
    }

    return pages;
  }

  Widget _buildCompanySelectionPage() {
    final companies = widget.insuranceData.keys.toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Company',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: companies.length,
            itemBuilder: (context, index) {
              final company = companies[index];
              return RadioListTile<String>(
                title: Text(company),
                value: company,
                groupValue: _selectedCompany,
                activeColor: const Color(0xFFe92933),
                onChanged: (value) {
                  setState(() {
                    _selectedCompany = value!;
                    // Reset dependent selections when company changes
                    final products = widget.insuranceData[_selectedCompany]?['products']?.keys.toList() ?? [];
                    if (products.isNotEmpty) {
                      _selectedProduct = products.first;
                      final plans = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?.keys.toList() ?? [];
                      if (plans.isNotEmpty) {
                        _selectedPlan = plans.first;
                        final sumInsuredOptions = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?[_selectedPlan] ?? [];
                        if (sumInsuredOptions.isNotEmpty) {
                          _selectedSumInsured = sumInsuredOptions.first;
                        }
                      }
                    }
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductSelectionPage() {
    final products = widget.insuranceData[_selectedCompany]?['products']?.keys.toList() ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.editType != EditType.company) ...[
          _buildLockedFieldInDialog('Company', _selectedCompany),
          const SizedBox(height: 16),
        ],
        const Text(
          'Select Product',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              return RadioListTile<String>(
                title: Text(product),
                value: product,
                groupValue: _selectedProduct,
                activeColor: const Color(0xFFe92933),
                onChanged: (value) {
                  setState(() {
                    _selectedProduct = value!;
                    // Reset dependent selections when product changes
                    final plans = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?.keys.toList() ?? [];
                    if (plans.isNotEmpty) {
                      _selectedPlan = plans.first;
                      final sumInsuredOptions = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?[_selectedPlan] ?? [];
                      if (sumInsuredOptions.isNotEmpty) {
                        _selectedSumInsured = sumInsuredOptions.first;
                      }
                    }
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPlanSelectionPage() {
    final plans = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?.keys.toList() ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.editType == EditType.plan) ...[
          _buildLockedFieldInDialog('Company', _selectedCompany),
          _buildLockedFieldInDialog('Product', _selectedProduct),
          const SizedBox(height: 16),
        ],
        const Text(
          'Select Plan',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: plans.length,
            itemBuilder: (context, index) {
              final plan = plans[index];
              return RadioListTile<String>(
                title: Text(plan),
                value: plan,
                groupValue: _selectedPlan,
                activeColor: const Color(0xFFe92933),
                onChanged: (value) {
                  setState(() {
                    _selectedPlan = value!;
                    // Reset dependent selections when plan changes
                    final sumInsuredOptions = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?[_selectedPlan] ?? [];
                    if (sumInsuredOptions.isNotEmpty) {
                      _selectedSumInsured = sumInsuredOptions.first;
                    }
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSumInsuredSelectionPage() {
    final sumInsuredOptions = widget.insuranceData[_selectedCompany]?['products']?[_selectedProduct]?['plans']?[_selectedPlan] ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select Sum Insured',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF111418),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: ListView.builder(
            itemCount: sumInsuredOptions.length,
            itemBuilder: (context, index) {
              final amount = sumInsuredOptions[index];
              return RadioListTile<String>(
                title: Text(amount),
                value: amount,
                groupValue: _selectedSumInsured,
                activeColor: const Color(0xFFe92933),
                onChanged: (value) {
                  setState(() {
                    _selectedSumInsured = value!;
                  });
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLockedFieldInDialog(String label, String value) {
    return Row(
      children: [
        Icon(
          Icons.lock,
          size: 16,
          color: Colors.grey[400],
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF637488),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Color(0xFF111418),
          ),
        ),
      ],
    );
  }

  void _goToNextStep() {
    if (_currentStep < _totalSteps - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > _getStartStep()) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _saveChanges() {
    Navigator.pop(context);
    widget.onSave(_selectedCompany, _selectedProduct, _selectedPlan, _selectedSumInsured);
  }
}

# Settings Screen

A comprehensive settings page for the All About Insurance app, built based on the provided HTML design.

## Features

### User Profile Header
- Displays user avatar, name, and email
- Shows premium badge for premium users
- Gradient background matching app theme
- Clickable header for profile navigation

### Search Functionality
- Real-time search through all settings
- Filters settings items by title and description
- Clean, Material Design search bar

### Settings Sections

#### Account
- **Profile**: Navigate to profile management
- **Subscription**: Manage premium subscription
- **Security**: Password and security settings

#### Notifications
- **Push Notifications**: Toggle device notifications
- **Email Notifications**: Toggle email updates

#### Privacy
- **Location Services**: Control location access
- **Analytics**: Control usage data sharing

#### Preferences
- **Dark Mode**: Toggle dark/light theme
- **Biometric Authentication**: Enable/disable biometric login
- **Auto Download**: Control automatic updates

#### Support
- **Help Center**: Access help and support
- **About**: App version and information

#### Account Actions
- **Sign Out**: Sign out with confirmation dialog
- **Delete Account**: Delete account with confirmation dialog

## Architecture

### Components
- `SettingsScreen`: Main settings screen widget
- `SettingsHeader`: User profile header component
- `SettingsSearchBar`: Search functionality component
- `SettingsSection`: Section container with title
- `SettingsItem`: Individual setting item with icon, text, and optional switch/navigation

### State Management
- Uses Riverpod for state management
- `SettingsProvider`: Manages all settings state
- `SettingsModel`: Data model for settings
- Persistent storage using SharedPreferences

### Navigation
- Integrated with app's navigation service
- Accessible from profile sidebar
- Proper back navigation and route management

## Design Principles

### Colors
- Primary red: `#e92933` for switches and accents
- Background: `#f1f1f1` (matching app theme)
- Gradient header: `#012853` to `#1b3e64`
- Card background: White with subtle shadows

### Typography
- Section titles: Uppercase, small, muted
- Setting titles: Medium weight, dark
- Descriptions: Regular weight, muted

### Layout
- Consistent 16px padding
- Rounded corners (12-16px radius)
- Subtle shadows for depth
- Proper spacing between sections

## Usage

### Navigation
Access settings through:
1. Profile sidebar → Settings
2. Direct navigation: `NavigationService.instance.navigateToSettings()`

### State Management
```dart
// Read settings
final settings = ref.watch(settingsProvider);

// Update settings
ref.read(settingsProvider.notifier).setPushNotifications(true);
```

### Customization
- Easy to add new settings sections
- Configurable colors and styling
- Extensible search functionality
- Modular component architecture

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/onboarding_service.dart';
import '../../services/navigation_service.dart';
import '../../providers/auth_provider.dart';
import '../../models/auth_state.dart';

class SplashScreen extends ConsumerStatefulWidget {
  final Widget Function() onboardingBuilder;
  final Widget Function() homeBuilder;
  final VoidCallback? onNavigateToOnboarding;
  final VoidCallback? onNavigateToHome;
  final VoidCallback? onNavigateToAuth;

  const SplashScreen({
    super.key,
    required this.onboardingBuilder,
    required this.homeBuilder,
    this.onNavigateToOnboarding,
    this.onNavigateToHome,
    this.onNavigateToAuth,
  });

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _fadeController;
  late Animation<double> _logoAnimation;
  late Animation<double> _fadeAnimation;
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startNavigationTimer();
  }

  void _setupAnimations() {
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _logoController.forward();
    _fadeController.forward();
  }

  void _startNavigationTimer() {
    // Wait for animations to complete, then listen for auth state
    Future.delayed(const Duration(milliseconds: 2500), () {
      if (mounted) {
        _handleAuthStateNavigation();
      }
    });
  }

  void _handleAuthStateNavigation() {
    final authState = ref.read(authProvider);

    // If auth is still initializing, listen for changes with timeout
    if (authState.status == AuthStatus.initial || authState.status == AuthStatus.loading) {
      // Set up timeout fallback (5 seconds max)
      Future.delayed(const Duration(seconds: 5), () {
        if (mounted && !_hasNavigated) {
          _navigateBasedOnAuthState(const AuthState.unauthenticated());
        }
      });

      // Listen for auth state changes
      ref.listen<AuthState>(authProvider, (previous, next) {
        if (mounted && !_hasNavigated && next.status != AuthStatus.initial && next.status != AuthStatus.loading) {
          _navigateBasedOnAuthState(next);
        }
      });
    } else {
      // Auth state is already determined, navigate immediately
      _navigateBasedOnAuthState(authState);
    }
  }

  void _navigateBasedOnAuthState(AuthState authState) async {
    if (!mounted || _hasNavigated) return;

    _hasNavigated = true;

    // If user is authenticated, go directly to home
    if (authState.status == AuthStatus.authenticated) {
      NavigationService.instance.navigateToHome();
      return;
    }

    // If user is not authenticated, check onboarding status
    final shouldShowOnboarding = await OnboardingService.instance.shouldShowOnboarding();

    if (mounted) {
      if (shouldShowOnboarding) {
        NavigationService.instance.navigateToOnboarding();
      } else {
        NavigationService.instance.navigateToAlternateSignIn();
      }
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo animation
            ScaleTransition(
              scale: _logoAnimation,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: const Color(0xFFe92933),
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFe92933).withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.shield_outlined,
                  size: 60,
                  color: Colors.white,
                ),
              ),
            ),

            const SizedBox(height: 32),

            // App name
            FadeTransition(
              opacity: _fadeAnimation,
              child: const Text(
                'All About Insurance',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFe92933),
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 8),

            // Tagline
            FadeTransition(
              opacity: _fadeAnimation,
              child: const Text(
                'Your trusted insurance companion',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 48),

            // Loading indicator
            FadeTransition(
              opacity: _fadeAnimation,
              child: const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFe92933)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

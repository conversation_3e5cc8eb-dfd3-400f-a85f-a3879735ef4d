import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/auth_state.dart';
import '../../providers/auth_provider.dart';
import '../../services/navigation_service.dart';

class AlternateLoginScreen extends ConsumerStatefulWidget {
  const AlternateLoginScreen({super.key});

  @override
  ConsumerState<AlternateLoginScreen> createState() => _AlternateLoginScreenState();
}

class _AlternateLoginScreenState extends ConsumerState<AlternateLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    // Always clear fields when this screen is created/initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _clearFields();
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }



  void _clearFields() {
    _emailController.clear();
    _passwordController.clear();
    setState(() {
      _obscurePassword = true;
    });
  }

  void _onLoginPressed() {
    if (_formKey.currentState?.validate() ?? false) {
      ref.read(authProvider.notifier).signInWithEmailAndPassword(
        email: _emailController.text,
        password: _passwordController.text,
      );
    }
  }

  void _onForgotPasswordPressed() {
    NavigationService.instance.navigateToAlternateForgotPassword();
  }

  void _onSignUpPressed() {
    NavigationService.instance.navigateToAlternateRegister();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Listen to auth state changes
    ref.listen<AuthState>(authProvider, (previous, next) {
      // Handle errors
      if (next.status == AuthStatus.error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Center(
              child: Text(
                next.errorMessage ?? 'An error occurred',
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      // Handle email verification pending
      else if (next.status == AuthStatus.verificationPending) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          NavigationService.instance.navigateToAlternateEmailVerification(
            email: _emailController.text,
            password: _passwordController.text,
          );
        });
      }

      // Handle authentication success
      else if (next.status == AuthStatus.authenticated) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          NavigationService.instance.navigateToHome();
        });
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Main Content - Centered
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                                // Welcome Text
                                _buildWelcomeText(),
                                
                                const SizedBox(height: 40),
                                
                                // Email Field
                                _buildEmailField(),
                                
                                const SizedBox(height: 16),
                                
                                // Password Field
                                _buildPasswordField(),
                                
                                const SizedBox(height: 24),
                                
                                // Forgot Password
                                _buildForgotPassword(),
                                
                                const SizedBox(height: 32),
                                
                                // Login Button
                                _buildLoginButton(authState),
                                
                                const SizedBox(height: 32),
                                
                          // Sign Up Link
                          _buildSignUpLink(),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

            // Header - Positioned at top
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: _buildHeader(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          IconButton(
            onPressed: () => NavigationService.instance.goBack(),
            icon: const Icon(
              Icons.arrow_back,
              color: Color(0xFFe92933),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeText() {
    return Column(
      children: [
        const Text(
          'Welcome Back to',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color(0xFF1a1a1a),
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'All About Insurance',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color(0xFFe92933),
          ),
        ),
      ],
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      style: const TextStyle(
        fontSize: 16,
        color: Color(0xFF1a1a1a),
      ),
      decoration: InputDecoration(
        labelText: 'Email',
        hintText: 'Email address',
        labelStyle: const TextStyle(
          color: Color(0xFF666666),
          fontSize: 14,
        ),
        hintStyle: const TextStyle(
          color: Color(0xFF666666),
          fontSize: 16,
        ),
        filled: true,
        fillColor: const Color(0xFFf2f2f2),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF666666),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF666666),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFe92933),
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      validator: (value) {
        final email = Email.dirty(value ?? '');
        if (email.error == EmailValidationError.empty) {
          return 'Email is required';
        }
        if (email.error == EmailValidationError.invalid) {
          return 'Please enter a valid email';
        }
        return null;
      },
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      style: const TextStyle(
        fontSize: 16,
        color: Color(0xFF1a1a1a),
      ),
      decoration: InputDecoration(
        labelText: 'Password',
        hintText: 'Password',
        labelStyle: const TextStyle(
          color: Color(0xFF666666),
          fontSize: 14,
        ),
        hintStyle: const TextStyle(
          color: Color(0xFF666666),
          fontSize: 16,
        ),
        filled: true,
        fillColor: const Color(0xFFf2f2f2),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF666666),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF666666),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFe92933),
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        suffixIcon: IconButton(
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
          icon: Icon(
            _obscurePassword ? Icons.visibility : Icons.visibility_off,
            color: const Color(0xFF666666),
          ),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Password is required';
        }
        return null;
      },
    );
  }

  Widget _buildForgotPassword() {
    return Align(
      alignment: Alignment.centerRight,
      child: GestureDetector(
        onTap: _onForgotPasswordPressed,
        child: const Text(
          'Forgot password?',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFFe92933),
          ),
        ),
      ),
    );
  }

  Widget _buildLoginButton(AuthState authState) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: authState.isLoading ? null : _onLoginPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFe92933),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: authState.isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Login',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Widget _buildSignUpLink() {
    return RichText(
      text: TextSpan(
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF666666),
        ),
        children: [
          const TextSpan(text: "New to All About Insurance? "),
          TextSpan(
            text: 'Sign up',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFFe92933),
            ),
            recognizer: TapGestureRecognizer()..onTap = _onSignUpPressed,
          ),
        ],
      ),
    );
  }
}

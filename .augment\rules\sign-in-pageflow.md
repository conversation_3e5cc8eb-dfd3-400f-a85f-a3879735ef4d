Login/Sign-up Page (vested)
Screen Design:
Login Screen:
o	Logo on left side of header. Button to signup on right side of header. 
o	Prominent Sign In text below header 
o	Option to go back to last screen with visible arrow
o	Login Options below sign in text:
	Login with mobile no to be primary login option.
	Other login opyions are below this option of login with mob and there is line separator between them
	But<PERSON> for "Login with Google", "Login with Apple" and “Login with Email”
	Login with google to open accounts.google.com screen to select google account
	Login with Apple to open applied.apple.com screen to sign in using apple id
	Login with email to open another screen with following details
1.	Login with email as text on top
2.	Option to go back to last screen with visible arrow
3.	Email address field with clear label and placeholder <NAME_EMAIL>
4.	Password field with clear label and placeholder text like ***********. It should have an option to show or hide password as well
5.	Forgot Password: An easy-to-spot "Forgot Password?" link right below the password field
6.	Then a login button
	Sign-Up Link: A subtle but noticeable "Don't have an account? Sign up" link/button below the login form.
Sign Up Screen:
o	Logo on left side of header. Button to sign in on right side of header. 
o	Prominent Sign Up text below header
o	Option to go back to last screen with visible arrow
o	Sign up Options  below sign up text
	Buttons for "Login with Google", "Login with Apple" and “Login with Email”
	Signup with google to open accounts.google.com screen to select google account
	signup with Apple to open applied.apple.com screen to sign in using apple id
	signup with email to open another screen with following details
1.	Logo on left side of header. Button to sign in on right side of header 
2.	Sign up with email as text on top
3.	Option to go back to last screen with visible arrow
4.	Email address field with clear label and placeholder <NAME_EMAIL>
5.	Password field with clear label and placeholder text like ***********. It should have an option to show or hide password as well
6.	Then a Sign up button. Upon clicking this new screen will open
1.	Logo on left side of header. Button to sign in on right side of header 
2.	Sign up with email as text on top
3.	Option to go back to last screen with visible arrow
4.	Add a brief confirmation message like "Reset link has been sent to your email."
5.	Show field to enter the verification code and a button to submit verification code
6.	Link to resend code below this field
7.	Once user inputs the correct verification code and click submit, user is redirected to home page 
	Login Link: A subtle but noticeable "Already have an account? Login" link/button below the login form. This will take user back to login screen
	Text that says that “By signing up you are accepting Terms and Conditions” with a link to read them.
Forgot Password Screen:
o	Logo on left side of header. Button to sign in on right side of header. 
o	Forgot password as text
o	Option to go back to last screen with visible arrow
o	Input field for entering the email or mobile number.
o	A "Send Verification Code" button.
Process Flow:
o	After entering the email/mobile, send a verification code or a password reset link.
o	Add a brief confirmation message like "Reset link has been sent to your email."
o	Show field to enter the verification code and a link to resend the code
o	Field to input new password and reconfirm the password just below verification code field
o	Once user inputs the verification code, new password and confirms the password and then click Reset Password Button, new screen to open 
o	show message that password has been changed and ask customer to login again. Provide an easy-to-spot "Login" link which will take user to login screen
Design Suggestions:
o	Use a clean and uncluttered layout.
o	Place your primary CTA (e.g., "Login") in a bright, distinct color.
o	Group similar actions visually (e.g., keep email/password fields together but separate them from third-party login buttons with spacing or dividers).
o	Add placeholders in input fields for guidance but ensure labels stay visible (e.g., floating labels).
Additional Features:
o	Option to show/hide the password for ease of use.
o	Save login info (e.g., "Remember me" checkbox).
Tips for a Great UX:
o	Use inline validations to give instant feedback (e.g., weak password warnings or "email already in use" prompts).
o	Keep the number of input fields minimal to reduce friction.
Additional Tips:
1.	Feedback: Provide users with clear feedback for all actions (e.g., "Login successful" or "Invalid credentials").
2.	Consistency: Ensure all related pages (login, sign-up, forgot password) follow a unified design language.
3.	Security: Encrypt sensitive data and implement reCAPTCHA to prevent bot attacks.


class OnboardingPage {
  final String title;
  final String description;
  final String imagePath;
  final String? animationPath;
  final String? subtitle;

  const OnboardingPage({
    required this.title,
    required this.description,
    required this.imagePath,
    this.animationPath,
    this.subtitle,
  });
}

class OnboardingData {
  static const List<OnboardingPage> pages = [
    OnboardingPage(
      title: 'Welcome to',
      subtitle: 'Your trusted insurance companion',
      description: 'Discover comprehensive insurance solutions tailored to protect what matters most to you and your family.',
      imagePath: 'assets/images/onboarding_welcome.png',
      animationPath: 'assets/animations/welcome.json',
    ),
    OnboardingPage(
      title: 'Compare & Choose',
      subtitle: 'Find the perfect coverage',
      description: 'Compare insurance plans from top providers and choose the coverage that fits your needs and budget.',
      imagePath: 'assets/images/onboarding_compare.png',
      animationPath: 'assets/animations/compare.json',
    ),
    OnboardingPage(
      title: 'Expert Guidance',
      subtitle: 'Professional support when you need',
      description: 'Get personalized advice from our insurance experts to make informed decisions about your coverage.',
      imagePath: 'assets/images/onboarding_expert.png',
      animationPath: 'assets/animations/expert.json',
    ),
    OnboardingPage(
      title: 'Get Started',
      subtitle: 'Begin your insurance journey',
      description: 'Ready to protect your future? Let\'s find the perfect insurance solution for you today.',
      imagePath: 'assets/images/onboarding_start.png',
      animationPath: 'assets/animations/start.json',
    ),
  ];
}

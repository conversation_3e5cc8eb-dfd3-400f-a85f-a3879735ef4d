import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/auth_state.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/auth/auth_text_field.dart';
import '../../widgets/auth/auth_buttons.dart';
import '../../utils/responsive_helper.dart';
import '../../services/navigation_service.dart';

class PhoneAuthScreen extends ConsumerStatefulWidget {
  const PhoneAuthScreen({super.key});

  @override
  ConsumerState<PhoneAuthScreen> createState() => _PhoneAuthScreenState();
}

class _PhoneAuthScreenState extends ConsumerState<PhoneAuthScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  
  bool _codeSent = false;
  String? _verificationId;
  String _otpCode = '';

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  void _onSendCodePressed() {
    if (_formKey.currentState?.validate() ?? false) {
      ref.read(authProvider.notifier).verifyPhoneNumber(
        phoneNumber: _phoneController.text,
        onCodeSent: (verificationId) {
          setState(() {
            _verificationId = verificationId;
            _codeSent = true;
          });
        },
      );
    }
  }

  void _onVerifyPressed() {
    if (_verificationId != null && _otpCode.length == 6) {
      ref.read(authProvider.notifier).signInWithPhoneOtp(
        verificationId: _verificationId!,
        otpCode: _otpCode,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final authState = ref.watch(authProvider);
    final horizontalPadding = ResponsiveHelper.getHorizontalPadding(context);
    final maxContentWidth = ResponsiveHelper.getMaxContentWidth(context);

    // Listen to auth state changes
    ref.listen<AuthState>(authProvider, (previous, next) {
      if (next.status == AuthStatus.error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Center(
              child: Text(
                next.errorMessage ?? 'An error occurred',
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
            backgroundColor: colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else if (next.status == AuthStatus.authenticated) {
        // Navigate to home screen (users can switch to alternate from there)
        WidgetsBinding.instance.addPostFrameCallback((_) {
          NavigationService.instance.navigateToHome();
        });
      }
    });

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFFe92933)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: BoxConstraints(maxWidth: maxContentWidth),
            child: SingleChildScrollView(
              padding: horizontalPadding,
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 32),
                    
                    if (!_codeSent) ...[
                      // Phone Number Entry
                      _buildPhoneEntry(theme, colorScheme, authState),
                    ] else ...[
                      // OTP Verification
                      _buildOtpVerification(theme, colorScheme, authState),
                    ],
                    
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPhoneEntry(ThemeData theme, ColorScheme colorScheme, AuthState authState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Phone Verification',
          style: ResponsiveHelper.getResponsiveTextStyle(
            context,
            theme.textTheme.displayMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Enter your phone number to receive a verification code',
          style: ResponsiveHelper.getResponsiveTextStyle(
            context,
            theme.textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        const SizedBox(height: 48),
        AuthTextField(
          label: 'Phone Number',
          hintText: '+****************',
          controller: _phoneController,
          keyboardType: TextInputType.phone,
          prefixIcon: Icon(
            Icons.phone_outlined,
            color: colorScheme.onSurfaceVariant,
          ),
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Phone number is required';
            }
            if (!RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(value!)) {
              return 'Please enter a valid phone number';
            }
            return null;
          },
        ),
        const SizedBox(height: 32),
        AuthButton(
          text: 'Send Code',
          onPressed: _onSendCodePressed,
          isLoading: authState.isLoading,
        ),
      ],
    );
  }

  Widget _buildOtpVerification(ThemeData theme, ColorScheme colorScheme, AuthState authState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Enter Verification Code',
          style: ResponsiveHelper.getResponsiveTextStyle(
            context,
            theme.textTheme.displayMedium?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'We sent a 6-digit code to ${_phoneController.text}',
          style: ResponsiveHelper.getResponsiveTextStyle(
            context,
            theme.textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        const SizedBox(height: 48),
        OtpInputField(
          onCompleted: (code) {
            setState(() {
              _otpCode = code;
            });
          },
        ),
        const SizedBox(height: 32),
        AuthButton(
          text: 'Verify Code',
          onPressed: _onVerifyPressed,
          isLoading: authState.isLoading,
          isEnabled: _otpCode.length == 6,
        ),
        const SizedBox(height: 16),
        Center(
          child: AuthTextButton(
            text: 'Resend Code',
            onPressed: () {
              setState(() {
                _codeSent = false;
                _otpCode = '';
              });
            },
          ),
        ),
      ],
    );
  }
}

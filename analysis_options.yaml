# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/generated_plugin_registrant.dart"
    - "build/**"
    - ".dart_tool/**"

linter:
  rules:
    # Disable noisy rules for development
    avoid_print: false  # Allow print statements for debugging
    prefer_single_quotes: true
    # Disable some overly strict rules
    avoid_redundant_argument_values: false
    prefer_const_constructors: false
    prefer_const_literals_to_create_immutables: false

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options

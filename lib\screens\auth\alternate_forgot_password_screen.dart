import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/auth_state.dart';
import '../../providers/auth_provider.dart';
import '../../services/navigation_service.dart';

class AlternateForgotPasswordScreen extends ConsumerStatefulWidget {
  const AlternateForgotPasswordScreen({super.key});

  @override
  ConsumerState<AlternateForgotPasswordScreen> createState() => _AlternateForgotPasswordScreenState();
}

class _AlternateForgotPasswordScreenState extends ConsumerState<AlternateForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _emailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  void _onSendPressed() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        await ref.read(authProvider.notifier).sendPasswordResetEmail(_emailController.text.trim());
        setState(() {
          _emailSent = true;
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Center(
                child: Text(
                  'If an account with this email exists, you will receive a password reset link.',
                  style: TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
              backgroundColor: Colors.blue,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } catch (e) {
        // Error handling is done in the provider listener
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Listen to auth state changes
    ref.listen<AuthState>(authProvider, (previous, next) {
      if (next.status == AuthStatus.error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Center(
              child: Text(
                next.errorMessage ?? 'An error occurred',
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Main Content - Centered
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (!_emailSent) ...[
                          // Title
                          _buildTitle(),
                          
                          const SizedBox(height: 16),
                          
                          // Description
                          _buildDescription(),
                          
                          const SizedBox(height: 40),
                          
                          // Email Field
                          _buildEmailField(),
                          
                          const SizedBox(height: 32),
                          
                          // Send Button
                          _buildSendButton(authState),
                        ] else ...[
                          // Success Message
                          _buildSuccessMessage(),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
            
            // Header - Positioned at top
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: _buildHeader(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          IconButton(
            onPressed: () => NavigationService.instance.goBack(),
            icon: const Icon(
              Icons.arrow_back,
              color: Color(0xFFe92933),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return const Text(
      'Forgot Password?',
      style: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Color(0xFF1a1a1a),
      ),
    );
  }

  Widget _buildDescription() {
    return const Text(
      'Enter your email address and we\'ll send you a link to reset your password.',
      style: TextStyle(
        fontSize: 16,
        color: Color(0xFF666666),
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      style: const TextStyle(
        fontSize: 16,
        color: Color(0xFF1a1a1a),
      ),
      decoration: InputDecoration(
        labelText: 'Email',
        hintText: 'Email address',
        labelStyle: const TextStyle(
          color: Color(0xFF666666),
          fontSize: 14,
        ),
        hintStyle: const TextStyle(
          color: Color(0xFF666666),
          fontSize: 16,
        ),
        filled: true,
        fillColor: const Color(0xFFf2f2f2),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF666666),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF666666),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFe92933),
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Email is required';
        }
        final email = Email.dirty(value.trim());
        if (email.error == EmailValidationError.invalid) {
          return 'Please enter a valid email address';
        }
        return null;
      },
    );
  }

  Widget _buildSendButton(AuthState authState) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: authState.isLoading ? null : _onSendPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFe92933),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: authState.isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Send Reset Link',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }



  Widget _buildSuccessMessage() {
    return Column(
      children: [
        const Icon(
          Icons.check_circle,
          color: Colors.green,
          size: 64,
        ),
        
        const SizedBox(height: 24),
        
        const Text(
          'Email Sent!',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color(0xFF1a1a1a),
          ),
        ),
        
        const SizedBox(height: 16),
        
        const Text(
          'We\'ve sent a password reset link to your email address.',
          style: TextStyle(
            fontSize: 16,
            color: Color(0xFF666666),
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 32),
      ],
    );
  }
}

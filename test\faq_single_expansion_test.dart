import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aai/screens/faq_screen.dart';

void main() {
  group('FAQ Single Expansion Tests', () {
    testWidgets('should display FAQ screen with question mark icon', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const FAQScreen(),
        ),
      );

      // Assert - Check for header elements
      expect(find.text('FAQs'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);
      
      // Assert - Check for question mark icon
      expect(find.byIcon(Icons.help_outline), findsOneWidget);
      
      // Assert - Check for FAQ items
      expect(find.text('How do I get started?'), findsOneWidget);
      expect(find.text('Is there a mobile app available?'), findsOneWidget);
    });

    testWidgets('should expand only one FAQ at a time', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const FAQScreen(),
        ),
      );

      // Initially no FAQs should be expanded
      expect(find.text('Getting started is easy!'), findsNothing);
      expect(find.text('Yes! Our mobile app is available'), findsNothing);

      // Act - Tap first FAQ to expand it
      await tester.tap(find.text('How do I get started?'));
      await tester.pumpAndSettle();

      // Assert - First FAQ should be expanded
      expect(find.text('Getting started is easy!'), findsOneWidget);
      expect(find.text('Yes! Our mobile app is available'), findsNothing);

      // Act - Tap second FAQ to expand it
      await tester.tap(find.text('Is there a mobile app available?'));
      await tester.pumpAndSettle();

      // Assert - Only second FAQ should be expanded, first should be closed
      expect(find.text('Getting started is easy!'), findsNothing);
      expect(find.text('Yes! Our mobile app is available'), findsOneWidget);
    });

    testWidgets('should close FAQ when tapped again', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const FAQScreen(),
        ),
      );

      // Act - Tap first FAQ to expand it
      await tester.tap(find.text('How do I get started?'));
      await tester.pumpAndSettle();

      // Assert - FAQ should be expanded
      expect(find.text('Getting started is easy!'), findsOneWidget);

      // Act - Tap the same FAQ again to close it
      await tester.tap(find.text('How do I get started?'));
      await tester.pumpAndSettle();

      // Assert - FAQ should be closed
      expect(find.text('Getting started is easy!'), findsNothing);
    });

    testWidgets('should maintain consistent header during scroll', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const FAQScreen(),
        ),
      );

      // Get initial header color
      final headerContainer = tester.widget<Container>(
        find.descendant(
          of: find.byType(SafeArea),
          matching: find.byType(Container),
        ).first,
      );
      final initialDecoration = headerContainer.decoration as BoxDecoration?;

      // Act - Scroll down
      await tester.drag(find.byType(SingleChildScrollView), const Offset(0, -300));
      await tester.pumpAndSettle();

      // Assert - Header should maintain same color
      final headerAfterScroll = tester.widget<Container>(
        find.descendant(
          of: find.byType(SafeArea),
          matching: find.byType(Container),
        ).first,
      );
      final decorationAfterScroll = headerAfterScroll.decoration as BoxDecoration?;

      expect(decorationAfterScroll?.color, equals(initialDecoration?.color));
    });
  });
}

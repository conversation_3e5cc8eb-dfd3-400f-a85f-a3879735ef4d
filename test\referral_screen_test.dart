import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aai/screens/referral_screen.dart';

void main() {
  group('ReferralScreen Tests', () {
    testWidgets('ReferralScreen displays correctly', (WidgetTester tester) async {
      // Build the ReferralScreen widget
      await tester.pumpWidget(
        const MaterialApp(
          home: ReferralScreen(),
        ),
      );

      // Verify that the screen displays the main elements
      expect(find.text('Refer Friends'), findsOneWidget);
      expect(find.text('Share the love and earn rewards together!'), findsOneWidget);
      expect(find.text('Your Referral Code'), findsOneWidget);
      expect(find.text('FRIEND2024'), findsOneWidget);
      expect(find.text('Copy Link'), findsOneWidget);
      expect(find.text('Share with <PERSON>'), findsOneWidget);
      expect(find.text('How it Works'), findsOneWidget);
      
      // Verify stats are displayed
      expect(find.text('12'), findsOneWidget);
      expect(find.text('Friends Joined'), findsOneWidget);
      expect(find.text('\$240'), findsOneWidget);
      expect(find.text('Rewards Earned'), findsOneWidget);
      
      // Verify share buttons
      expect(find.text('WhatsApp'), findsOneWidget);
      expect(find.text('Twitter'), findsOneWidget);
      expect(find.text('Facebook'), findsOneWidget);
      
      // Verify how it works steps
      expect(find.text('Share your code'), findsOneWidget);
      expect(find.text('Friend joins'), findsOneWidget);
      expect(find.text('Both earn rewards'), findsOneWidget);
    });

    testWidgets('Copy button works', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ReferralScreen(),
        ),
      );

      // Find and tap the copy button
      final copyButton = find.text('Copy Link');
      expect(copyButton, findsOneWidget);
      
      await tester.tap(copyButton);
      await tester.pump();

      // Verify snackbar appears
      expect(find.text('Referral code copied to clipboard!'), findsOneWidget);
    });

    testWidgets('Share buttons work', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: ReferralScreen(),
        ),
      );

      // Scroll to make the share buttons visible
      await tester.scrollUntilVisible(
        find.text('WhatsApp'),
        500.0,
        scrollable: find.byType(SingleChildScrollView),
      );

      // Test WhatsApp share button
      final whatsappButton = find.text('WhatsApp');
      expect(whatsappButton, findsOneWidget);

      await tester.tap(whatsappButton);
      await tester.pump();

      expect(find.text('Opening WhatsApp...'), findsOneWidget);
    });

    testWidgets('Back button works', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const ReferralScreen()),
                ),
                child: const Text('Go to Referral'),
              ),
            ),
          ),
        ),
      );

      // Navigate to referral screen
      await tester.tap(find.text('Go to Referral'));
      await tester.pumpAndSettle();

      // Verify we're on the referral screen
      expect(find.text('Refer Friends'), findsOneWidget);

      // Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Verify we're back to the original screen
      expect(find.text('Go to Referral'), findsOneWidget);
    });
  });
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aai/screens/products/product_details_screen.dart';

void main() {
  group('ProductDetailsScreen Tabs Tests', () {
    testWidgets('should display tabs with correct styling', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const ProductDetailsScreen(
            productName: 'Health Insurance',
            productDescription: 'Comprehensive health coverage',
            companyName: 'SecureHealth',
            companyId: 'securehealth',
          ),
        ),
      );

      // Assert - Check for tabs (now 4 tabs including Links)
      expect(find.text('Benefits'), findsOneWidget);
      expect(find.text('Premium'), findsOneWidget);
      expect(find.text('Documents'), findsOneWidget);
      expect(find.text('Links'), findsOneWidget);
    });

    testWidgets('should switch tabs when tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const ProductDetailsScreen(
            productName: 'Health Insurance',
            productDescription: 'Comprehensive health coverage',
            companyName: 'SecureHealth',
            companyId: 'securehealth',
          ),
        ),
      );

      // Initially Benefits tab should be selected (default)
      expect(find.text('Benefits'), findsOneWidget);

      // Act - Tap Premium tab
      await tester.tap(find.text('Premium'));
      await tester.pumpAndSettle();

      // Assert - Premium content should be visible
      expect(find.text('Premium Details'), findsOneWidget);
      expect(find.text('Annual Premium'), findsOneWidget);

      // Act - Tap Documents tab
      await tester.tap(find.text('Documents'));
      await tester.pumpAndSettle();

      // Assert - Documents content should be visible
      expect(find.text('Required Documents'), findsOneWidget);
      expect(find.text('Policy Document'), findsOneWidget);

      // Act - Tap Links tab
      await tester.tap(find.text('Links'));
      await tester.pumpAndSettle();

      // Assert - Links content should be visible
      expect(find.text('Useful Links'), findsOneWidget);
      expect(find.text('Policy Terms & Conditions'), findsOneWidget);
    });

    testWidgets('should have correct tab colors', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: const ProductDetailsScreen(
            productName: 'Health Insurance',
            productDescription: 'Comprehensive health coverage',
            companyName: 'SecureHealth',
            companyId: 'securehealth',
          ),
        ),
      );

      // Find the Benefits tab container (should be selected by default)
      final benefitsTab = find.ancestor(
        of: find.text('Benefits'),
        matching: find.byType(Container),
      ).first;

      // Get the container widget
      final Container benefitsContainer = tester.widget(benefitsTab);
      final BoxDecoration? decoration = benefitsContainer.decoration as BoxDecoration?;

      // Assert - Selected tab should have red background
      expect(decoration?.color, equals(const Color(0xFFe92933)));
    });
  });
}

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/navigation_service.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/profile_sidebar.dart';
import '../companies/all_companies_screen.dart';
import '../products/product_details_screen.dart';

class AlternateHomeScreen extends ConsumerStatefulWidget {
  const AlternateHomeScreen({super.key});

  @override
  ConsumerState<AlternateHomeScreen> createState() => _AlternateHomeScreenState();
}

class _AlternateHomeScreenState extends ConsumerState<AlternateHomeScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  int _selectedInsuranceTabIndex = 0;
  int _currentCarouselIndex = 0;
  final PageController _pageController = PageController(viewportFraction: 0.92);
  final PageController _policiesPageController = PageController(viewportFraction: 0.92);
  final ScrollController _tabScrollController = ScrollController();
  Timer? _autoScrollTimer;
  static const int _totalCarouselItems = 5;
  static const Duration _autoScrollDuration = Duration(seconds: 4);

  // State for expandable companies - only one can be expanded at a time
  String? _expandedCompanyId;

  @override
  void initState() {
    super.initState();
    // Start auto-scroll after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startAutoScroll();
    });
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _pageController.dispose();
    _policiesPageController.dispose();
    _tabScrollController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    _autoScrollTimer?.cancel(); // Cancel any existing timer
    _autoScrollTimer = Timer.periodic(_autoScrollDuration, (timer) {
      if (mounted && _pageController.hasClients) {
        final nextIndex = (_currentCarouselIndex + 1) % _totalCarouselItems;
        _pageController.animateToPage(
          nextIndex,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _stopAutoScroll() {
    _autoScrollTimer?.cancel();
  }

  void _restartAutoScroll() {
    _stopAutoScroll();
    _startAutoScroll();
  }

  void _onTabChanged(int index) {
    setState(() {
      _selectedInsuranceTabIndex = index;
    });

    // Auto-scroll to bring selected tab into view
    _scrollToTab(index);

    // Reset policies PageView to first page when tab changes
    if (_policiesPageController.hasClients) {
      _policiesPageController.animateToPage(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }

    // Note: This PageController is for carousel, not tabs
    // In a real implementation, you'd have separate content for each tab
  }

  void _scrollToTab(int index) {
    if (!_tabScrollController.hasClients) return;

    // Calculate the position of the selected tab
    const double tabWidth = 80.0; // Fixed width for each tab
    const double tabMargin = 8.0; // 4px margin on each side
    const double leftPadding = 16.0; // Left padding of the scroll view

    // Calculate the total width per tab (width + margins)
    const double totalTabWidth = tabWidth + tabMargin;

    // Calculate the position of the selected tab
    final double targetPosition = leftPadding + (index * totalTabWidth);

    // Get the viewport width
    final double viewportWidth = _tabScrollController.position.viewportDimension;

    // Calculate the scroll position to center the tab in the viewport
    final double scrollPosition = targetPosition - (viewportWidth / 2) + (tabWidth / 2);

    // Clamp the scroll position to valid bounds
    final double maxScrollExtent = _tabScrollController.position.maxScrollExtent;
    final double clampedPosition = scrollPosition.clamp(0.0, maxScrollExtent);

    // Animate to the calculated position
    _tabScrollController.animateTo(
      clampedPosition,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }



  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent default back button behavior
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        // On home screen, minimize the app instead of exiting
        SystemNavigator.pop();
      },
      child: Scaffold(
      key: _scaffoldKey,
      backgroundColor: const Color(0xFFf1f1f1),
      drawer: SizedBox(
        width: MediaQuery.of(context).size.width * 0.85,
        child: Drawer(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(4),
              bottomRight: Radius.circular(4),
            ),
          ),
          child: Consumer(
            builder: (context, ref, child) {
              final currentUser = ref.watch(currentUserProvider);
              return ProfileSidebar(
                userName: currentUser?.displayName ?? 'Name',
                userEmail: currentUser?.email ?? '<EMAIL>',
                userPhone: currentUser?.phoneNumber ?? '+91 - - - - - - - - - -',
                profileImageUrl: currentUser?.photoURL,
                onLogout: () {
                  Navigator.pop(context); // Close drawer
                  ref.read(authProvider.notifier).signOut();
                  NavigationService.instance.navigateToAlternateSignIn();
                },
                onEditProfile: () {
                  Navigator.pop(context); // Close drawer
                  NavigationService.instance.navigateToProfile();
                },
              );
            },
          ),
        ),
      ),
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildHeroBanner(),
                  _buildTabSection(),
                  _buildTopPolicies(),
                  _buildAlternateTopCompanies(),
                  _buildShortcuts(),
                  const SizedBox(height: 100), // Space for bottom nav
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigation(),
      ), // Close Scaffold
    ); // Close PopScope
  }

  Widget _buildHeader() {
    return Container(
      color: const Color(0xFFf1f1f1),
      child: SafeArea(
        bottom: false,
        child: Container(
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: const BoxDecoration(
            color: Color(0xFFf1f1f1),
            boxShadow: [
              BoxShadow(
                color: Color(0x0F000000),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              const Spacer(),
              // Demo button
              TextButton(
                onPressed: () {
                  NavigationService.instance.navigateToProfileDemo();
                },
                style: TextButton.styleFrom(
                  backgroundColor: const Color(0xFFe92933).withValues(alpha: 0.1),
                  foregroundColor: const Color(0xFFe92933),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  minimumSize: Size.zero,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: const Text(
                  'Demo',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: () {
                  NavigationService.instance.navigateToNotifications();
                },
                icon: Stack(
                  children: [
                    const Icon(
                      Icons.notifications_outlined,
                      color: Colors.black,
                      size: 24,
                    ),
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: () {
                  // Open profile sidebar
                  _scaffoldKey.currentState?.openDrawer();
                },
                icon: const Icon(
                  Icons.menu,
                  color: Color(0xFFe92933),
                  size: 24,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeroBanner() {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 16),
          height: 200,
          child: NotificationListener<ScrollNotification>(
            onNotification: (scrollNotification) {
              if (scrollNotification is ScrollStartNotification) {
                _stopAutoScroll();
              } else if (scrollNotification is ScrollEndNotification) {
                _restartAutoScroll();
              }
              return false;
            },
            child: PageView.builder(
              controller: _pageController, // Use class-level controller with viewportFraction
              itemCount: _totalCarouselItems,
              onPageChanged: (index) {
                setState(() {
                  _currentCarouselIndex = index;
                });
              },
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8), // Space between cards
                  child: _buildBannerCardByIndex(index),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBannerCard({
    required String title,
    required String subtitle,
    required String buttonText,
    required Gradient gradient,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: gradient,
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            if (buttonText.isNotEmpty)
              ElevatedButton(
                onPressed: () {
                  // TODO: Implement button action
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF146dc9),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                ),
                child: Text(
                  buttonText,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBannerCardByIndex(int index) {
    switch (index) {
      case 0:
        return _buildBannerCard(
          title: 'Refer a friend, get a free month',
          subtitle: 'Share the benefits of our service and enjoy a complimentary month.',
          buttonText: '',
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF086788), Color(0xFF086788)],
          ),
        );
      case 1:
        return _buildBannerCard(
          title: 'Boost Your Earnings',
          subtitle: 'Explore new ways to maximize your commissions and grow your business.',
          buttonText: '',
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF157A6E), Color(0xFF157A6E)],
          ),
        );
      case 2:
        return _buildBannerCard(
          title: 'Protect Your Family',
          subtitle: 'Comprehensive life insurance plans to secure your loved ones future.',
          buttonText: '',
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF306B34), Color(0xFF306B34)],
          ),
        );
      case 3:
        return _buildBannerCard(
          title: 'Health First Priority',
          subtitle: 'Complete health coverage for you and your family with cashless benefits.',
          buttonText: '',
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF656565), Color(0xFF656565)],
          ),
        );
      case 4:
        return _buildBannerCard(
          title: 'Drive with Confidence',
          subtitle: 'Comprehensive motor insurance with instant claim settlement.',
          buttonText: '',
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF34344A), Color(0xFF34344A)],
          ),
        );
      default:
        return _buildBannerCard(
          title: 'Default Card',
          subtitle: 'Default content',
          buttonText: '',
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF607D8B), Color(0xFF455A64)],
          ),
        );
    }
  }

  Widget _buildTabSection() {
    return Column(
      children: [
        SingleChildScrollView(
          controller: _tabScrollController,
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              const SizedBox(width: 16), // Left padding
              _buildTab('Health', 0),
              _buildTab('Life', 1),
              _buildTab('Motor', 2),
              _buildTab('Top Up', 3),
              _buildTab('Travel', 4),
              const SizedBox(width: 16), // Right padding
            ],
          ),
        ),
        // Running line below all tabs
        Container(
          height: 1,
          width: double.infinity,
          color: const Color(0xFFE0E0E0),
          margin: const EdgeInsets.symmetric(horizontal: 16),
        ),
      ],
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedInsuranceTabIndex == index;
    return GestureDetector(
      onTap: () => _onTabChanged(index),
      child: Container(
        width: 80, // Fixed width for each tab
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFe92933) : null,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
            bottomLeft: Radius.circular(0),
            bottomRight: Radius.circular(0),
          ),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: isSelected ? Colors.white : const Color(0xFF086788),
          ),
        ),
      ),
    );
  }

  Widget _buildTopPolicies() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Top Policies',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF111418),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AllCompaniesScreen(),
                    ),
                  );
                },
                child: const Icon(
                  Icons.arrow_forward,
                  color: Color(0xFFe92933),
                  size: 24,
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 240, // Height to accommodate 3 stacked cards
          child: PageView.builder(
            controller: _policiesPageController, // Use dedicated controller
            itemCount: _getPageCountForCurrentTab(), // Dynamic page count
            itemBuilder: (context, pageIndex) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 8), // Same as carousel
                child: Column(
                    children: _getPoliciesForCurrentTab(pageIndex),
                  ),
                );
              },
            ),
          ),
        ],
      );
  }

  int _getPageCountForCurrentTab() {
    switch (_selectedInsuranceTabIndex) {
      case 0: // Health - 9 policies (3 pages)
        return 3;
      case 1: // Life - 8 policies (3 pages)
        return 3;
      case 2: // Motor - 7 policies (3 pages)
        return 3;
      case 3: // Top Up - 5 policies (2 pages)
        return 2;
      case 4: // Travel - 6 policies (2 pages)
        return 2;
      default:
        return 3;
    }
  }

  List<Widget> _getPoliciesForCurrentTab(int pageIndex) {
    switch (_selectedInsuranceTabIndex) {
      case 0: // Health
        return _getHealthPolicies(pageIndex);
      case 1: // Life
        return _getLifePolicies(pageIndex);
      case 2: // Motor
        return _getMotorPolicies(pageIndex);
      case 3: // Top Up
        return _getTopUpPolicies(pageIndex);
      case 4: // Travel
        return _getTravelPolicies(pageIndex);
      default:
        return _getHealthPolicies(pageIndex);
    }
  }

  List<Widget> _getHealthPolicies(int pageIndex) {
    if (pageIndex == 0) {
      return [
        _buildPolicyCard(
          'Comprehensive Health',
          'Covers all major medical expenses.',
          Icons.health_and_safety,
        ),
        _buildPolicyCard(
          'Family Floater Plan',
          'One policy for the entire family.',
          Icons.groups,
        ),
        _buildPolicyCard(
          'Critical Illness',
          'Coverage for serious medical conditions.',
          Icons.local_hospital,
        ),
      ];
    } else if (pageIndex == 1) {
      return [
        _buildPolicyCard(
          'Senior Citizen Health',
          'Specialized health plans for seniors.',
          Icons.elderly,
        ),
        _buildPolicyCard(
          'Maternity Cover',
          'Complete maternity and newborn care.',
          Icons.pregnant_woman,
        ),
        _buildPolicyCard(
          'Dental & Vision',
          'Comprehensive dental and eye care.',
          Icons.visibility,
        ),
      ];
    } else {
      return [
        _buildPolicyCard(
          'Personal Accident',
          'Protection against accidents.',
          Icons.healing,
        ),
        _buildPolicyCard(
          'Health Top-Up',
          'Additional health coverage.',
          Icons.add_circle,
        ),
        _buildViewAllPoliciesCard(),
      ];
    }
  }

  List<Widget> _getLifePolicies(int pageIndex) {
    if (pageIndex == 0) {
      return [
        _buildPolicyCard(
          'Term Life Insurance',
          'Secure your family\'s future.',
          Icons.family_restroom,
        ),
        _buildPolicyCard(
          'Whole Life Insurance',
          'Lifelong protection with savings.',
          Icons.security,
        ),
        _buildPolicyCard(
          'Child Education Plan',
          'Secure your child\'s education.',
          Icons.school,
        ),
      ];
    } else if (pageIndex == 1) {
      return [
        _buildPolicyCard(
          'Retirement Plan',
          'Build your retirement corpus.',
          Icons.savings,
        ),
        _buildPolicyCard(
          'ULIP Plans',
          'Investment with insurance.',
          Icons.trending_up,
        ),
        _buildPolicyCard(
          'Money Back Policy',
          'Regular returns with protection.',
          Icons.account_balance_wallet,
        ),
      ];
    } else {
      return [
        _buildPolicyCard(
          'Group Life Insurance',
          'Employee life insurance plans.',
          Icons.groups,
        ),
        _buildPolicyCard(
          'Joint Life Policy',
          'Coverage for couples.',
          Icons.favorite,
        ),
      ];
    }
  }

  List<Widget> _getMotorPolicies(int pageIndex) {
    if (pageIndex == 0) {
      return [
        _buildPolicyCard(
          'Comprehensive Motor',
          'Complete vehicle protection.',
          Icons.directions_car,
        ),
        _buildPolicyCard(
          'Third Party Insurance',
          'Mandatory legal coverage.',
          Icons.gavel,
        ),
        _buildPolicyCard(
          'Two Wheeler Insurance',
          'Protection for bikes and scooters.',
          Icons.two_wheeler,
        ),
      ];
    } else if (pageIndex == 1) {
      return [
        _buildPolicyCard(
          'Commercial Vehicle',
          'Insurance for business vehicles.',
          Icons.local_shipping,
        ),
        _buildPolicyCard(
          'Zero Depreciation',
          'Full value claim coverage.',
          Icons.new_releases,
        ),
        _buildPolicyCard(
          'Engine Protection',
          'Coverage for engine damage.',
          Icons.build,
        ),
      ];
    } else {
      return [
        _buildPolicyCard(
          'Roadside Assistance',
          '24/7 emergency support.',
          Icons.support_agent,
        ),
      ];
    }
  }

  List<Widget> _getTopUpPolicies(int pageIndex) {
    if (pageIndex == 0) {
      return [
        _buildPolicyCard(
          'Health Top-Up',
          'Additional health coverage.',
          Icons.add_circle,
        ),
        _buildPolicyCard(
          'Super Top-Up',
          'Enhanced coverage limits.',
          Icons.workspace_premium,
        ),
        _buildPolicyCard(
          'Family Top-Up',
          'Extended family protection.',
          Icons.family_restroom,
        ),
      ];
    } else {
      return [
        _buildPolicyCard(
          'Critical Illness Top-Up',
          'Additional critical illness cover.',
          Icons.local_hospital,
        ),
        _buildPolicyCard(
          'Maternity Top-Up',
          'Enhanced maternity benefits.',
          Icons.pregnant_woman,
        ),
      ];
    }
  }

  List<Widget> _getTravelPolicies(int pageIndex) {
    if (pageIndex == 0) {
      return [
        _buildPolicyCard(
          'International Travel',
          'Protection for overseas trips.',
          Icons.flight,
        ),
        _buildPolicyCard(
          'Domestic Travel',
          'Coverage for local journeys.',
          Icons.train,
        ),
        _buildPolicyCard(
          'Student Travel',
          'Insurance for studying abroad.',
          Icons.school,
        ),
      ];
    } else {
      return [
        _buildPolicyCard(
          'Business Travel',
          'Corporate travel protection.',
          Icons.business_center,
        ),
        _buildPolicyCard(
          'Family Travel',
          'Group travel insurance.',
          Icons.family_restroom,
        ),
        _buildPolicyCard(
          'Adventure Sports',
          'Coverage for extreme activities.',
          Icons.sports,
        ),
      ];
    }
  }

  Widget _buildPolicyCard(String title, String subtitle, IconData icon) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProductDetailsScreen(
              productName: title,
              productDescription: subtitle,
              companyName: 'Insurance Company', // Default company name
              companyId: 'default_company',
            ),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(0, 8, 16, 8), // left: 0px, top: 8px, right: 16px, bottom: 8px
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0E7FF),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: const Color(0xFF197FE5),
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF111418),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF637488),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildViewAllPoliciesCard() {
    return GestureDetector(
      onTap: () {
        // TODO: Navigate to all policies page
        print('Navigate to all policies page');
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(0, 8, 16, 8),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0E7FF),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.view_list,
                    color: Color(0xFF197FE5),
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'View All Policies',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF111418),
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'Explore all insurance products.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF637488),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopCompanies() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Top Companies',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF111418),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AllCompaniesScreen(),
                    ),
                  );
                },
                child: const Icon(
                  Icons.arrow_forward,
                  color: Color(0xFFe92933),
                  size: 24,
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 240, // Height to accommodate 3 stacked cards
          child: PageView.builder(
            controller: PageController(viewportFraction: 0.92), // Same as carousel
            itemCount: 3, // 3 pages of 3 cards each (total 9 cards)
            itemBuilder: (context, pageIndex) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 8), // Same as carousel
                child: Column(
                  children: [
                    if (pageIndex == 0) ...[
                      _buildCompanyCard(
                        'SecureHealth',
                        'Best in health insurance.',
                        Icons.security,
                      ),
                      _buildCompanyCard(
                        'LifeGuard',
                        'Leading life insurance provider.',
                        Icons.shield,
                      ),
                      _buildCompanyCard(
                        'TravelSafe',
                        'Trusted travel insurance partner.',
                        Icons.flight_takeoff,
                      ),
                    ] else if (pageIndex == 1) ...[
                      _buildCompanyCard(
                        'AutoProtect',
                        'Complete motor insurance solutions.',
                        Icons.directions_car,
                      ),
                      _buildCompanyCard(
                        'HomeGuard',
                        'Comprehensive home protection.',
                        Icons.home,
                      ),
                      _buildCompanyCard(
                        'FamilyCare',
                        'Family-focused insurance plans.',
                        Icons.family_restroom,
                      ),
                    ] else ...[
                      _buildCompanyCard(
                        'BusinessShield',
                        'Commercial insurance expertise.',
                        Icons.business,
                      ),
                      _buildCompanyCard(
                        'WealthProtect',
                        'Investment and wealth protection.',
                        Icons.account_balance,
                      ),
                      _buildViewAllCompaniesCard(),
                    ],
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCompanyCard(String name, String description, IconData icon) {
    return Container(
      padding: const EdgeInsets.fromLTRB(0, 8, 16, 8), // Same as policy cards
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: const Color(0xFFE0E7FF),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: const Color(0xFF197FE5),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF111418),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF637488),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildViewAllCompaniesCard() {
    return GestureDetector(
      onTap: () {
        // TODO: Navigate to all companies page
        print('Navigate to all companies page');
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(0, 8, 16, 8),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0E7FF),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.business_center,
                    color: Color(0xFF146dc9),
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'View All Companies',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF111418),
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'Explore all insurance providers.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF637488),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpandableCompanyCard(
    String name,
    String description,
    IconData icon,
    String companyId,
    List<Map<String, String>> products,
  ) {
    final isExpanded = _expandedCompanyId == companyId;

    return Container(
      padding: const EdgeInsets.fromLTRB(0, 8, 16, 8), // Same as original cards
      child: Column(
        children: [
          // Main company card - matching original structure exactly
          InkWell(
            onTap: () {
              setState(() {
                if (isExpanded) {
                  // If this card is expanded, collapse it
                  _expandedCompanyId = null;
                } else {
                  // If this card is not expanded, expand it (and collapse any other)
                  _expandedCompanyId = companyId;
                }
              });
            },
            child: Row(
              children: [
                Container(
                  width: 64, // Same as original
                  height: 64, // Same as original
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0E7FF), // Same as original
                    borderRadius: BorderRadius.circular(8), // Same as original
                  ),
                  child: Icon(
                    icon,
                    color: const Color(0xFF197FE5), // Same as original
                    size: 32, // Same as original
                  ),
                ),
                const SizedBox(width: 16), // Same as original
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 14, // Same as original
                          fontWeight: FontWeight.w600, // Same as original
                          color: Color(0xFF111418), // Same as original
                        ),
                      ),
                      const SizedBox(height: 4), // Same as original
                      Text(
                        description,
                        style: const TextStyle(
                          fontSize: 14, // Same as original
                          color: Color(0xFF637488), // Same as original
                        ),
                      ),
                    ],
                  ),
                ),
                // Add dropdown arrow
                AnimatedRotation(
                  turns: isExpanded ? 0.5 : 0,
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: isExpanded ? const Color(0xFFe92933) : const Color(0xFF637488),
                    size: 24,
                  ),
                ),
              ],
            ),
          ),

          // Expandable products section
          if (isExpanded) ...[
            const SizedBox(height: 8),
            SizedBox(
              height: 70, // Reduced height to fit better in the layout
              width: double.infinity, // Full width
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.zero, // Remove all padding for left alignment
                itemCount: products.length,
                itemBuilder: (context, index) {
                  final product = products[index];
                  return Container(
                    width: 90, // Reduced width to fit more cards
                    margin: EdgeInsets.only(
                      left: index == 0 ? 0 : 8, // No left margin for first card, 8px for others
                    ),
                    child: _buildProductCard(
                      product['name']!,
                      product['description']!,
                      name, // company name
                      companyId, // company id
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProductCard(String name, String description, String companyName, String companyId) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFf8f9fa),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProductDetailsScreen(
                productName: name,
                productDescription: description,
                companyName: companyName,
                companyId: companyId,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(6),
        child: Padding(
          padding: const EdgeInsets.all(6), // Very compact padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 10, // Very small font for compact layout
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF111418),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2), // Minimal spacing
              Expanded(
                child: Text(
                  description,
                  style: const TextStyle(
                    fontSize: 8, // Very small font for compact layout
                    color: Color(0xFF637488),
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAlternateTopCompanies() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Top Companies',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF111418),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AllCompaniesScreen(),
                    ),
                  );
                },
                child: const Icon(
                  Icons.arrow_forward,
                  color: Color(0xFFe92933),
                  size: 24,
                ),
              ),
            ],
          ),
        ),
        // Use PageView with proper snapping and alignment
        SizedBox(
          height: _expandedCompanyId != null ? 318 : 240, // Exact calculated heights (no SizedBox spacing)
          child: PageView.builder(
            controller: PageController(viewportFraction: 0.92), // Enable sneak peek
            itemCount: 3, // 3 pages
            itemBuilder: (context, pageIndex) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 8), // Same margin as regular cards
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: _getPageContent(pageIndex),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  List<Widget> _getPageContent(int pageIndex) {
    switch (pageIndex) {
      case 0:
        return [
          _buildExpandableCompanyCard(
            'SecureHealth',
            'Best in health insurance.',
            Icons.security,
            'securehealth_exp',
            [
              {'name': 'Health Insurance', 'description': 'Comprehensive health coverage'},
              {'name': 'Family Plans', 'description': 'Complete family protection'},
              {'name': 'Senior Citizen', 'description': 'Elderly care plans'},
              {'name': 'Critical Illness', 'description': 'Serious disease coverage'},
              {'name': 'Maternity Care', 'description': 'Pregnancy & childbirth'},
              {'name': 'Dental Care', 'description': 'Oral health coverage'},
            ],
          ),
          _buildExpandableCompanyCard(
            'LifeGuard',
            'Leading life insurance provider.',
            Icons.shield,
            'lifeguard_exp',
            [
              {'name': 'Term Life', 'description': 'Pure life protection'},
              {'name': 'Whole Life', 'description': 'Lifetime coverage'},
              {'name': 'ULIP Plans', 'description': 'Investment + insurance'},
            ],
          ),
          _buildExpandableCompanyCard(
            'TravelSafe',
            'Trusted travel insurance partner.',
            Icons.flight_takeoff,
            'travelsafe_exp',
            [
              {'name': 'Domestic Travel', 'description': 'India travel coverage'},
              {'name': 'International', 'description': 'Worldwide protection'},
              {'name': 'Student Travel', 'description': 'Study abroad plans'},
              {'name': 'Business Travel', 'description': 'Corporate coverage'},
              {'name': 'Adventure Sports', 'description': 'High-risk activities'},
            ],
          ),
        ];
      case 1:
        return [
          _buildExpandableCompanyCard(
            'AutoProtect',
            'Complete motor insurance solutions.',
            Icons.directions_car,
            'autoprotect_exp',
            [
              {'name': 'Car Insurance', 'description': 'Comprehensive car coverage'},
              {'name': 'Bike Insurance', 'description': 'Two-wheeler protection'},
              {'name': 'Commercial Vehicle', 'description': 'Fleet insurance'},
            ],
          ),
          _buildExpandableCompanyCard(
            'HomeGuard',
            'Comprehensive home protection.',
            Icons.home,
            'homeguard_exp',
            [
              {'name': 'Home Insurance', 'description': 'Property protection'},
              {'name': 'Contents Cover', 'description': 'Belongings safety'},
              {'name': 'Tenant Insurance', 'description': 'Rental protection'},
            ],
          ),
          _buildExpandableCompanyCard(
            'FamilyCare',
            'Family-focused insurance plans.',
            Icons.family_restroom,
            'familycare_exp',
            [
              {'name': 'Family Health', 'description': 'Complete family coverage'},
              {'name': 'Child Plans', 'description': 'Kids future security'},
              {'name': 'Women Health', 'description': 'Specialized women care'},
            ],
          ),
        ];
      case 2:
        return [
          _buildExpandableCompanyCard(
            'BusinessShield',
            'Commercial insurance expertise.',
            Icons.business,
            'businessshield_exp',
            [
              {'name': 'Business Insurance', 'description': 'Commercial protection'},
              {'name': 'Professional Indemnity', 'description': 'Professional liability'},
              {'name': 'Cyber Security', 'description': 'Digital protection'},
            ],
          ),
          _buildExpandableCompanyCard(
            'WealthProtect',
            'Investment and wealth protection.',
            Icons.account_balance,
            'wealthprotect_exp',
            [
              {'name': 'Investment Plans', 'description': 'Wealth building'},
              {'name': 'Pension Plans', 'description': 'Retirement security'},
              {'name': 'Tax Saver', 'description': 'Tax efficient plans'},
            ],
          ),
          _buildViewAllExpandableCompaniesCard(),
        ];
      default:
        return [];
    }
  }

  Widget _buildViewAllExpandableCompaniesCard() {
    return GestureDetector(
      onTap: () {
        // TODO: Navigate to all companies page
        print('Navigate to all companies page');
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(0, 8, 16, 8), // Same as original cards
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 64, // Same as original
                  height: 64, // Same as original
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0E7FF), // Same as original
                    borderRadius: BorderRadius.circular(8), // Same as original
                  ),
                  child: const Icon(
                    Icons.business_center,
                    color: Color(0xFF146dc9),
                    size: 32, // Same as original
                  ),
                ),
                const SizedBox(width: 16), // Same as original
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'View All Companies',
                        style: TextStyle(
                          fontSize: 14, // Same as original
                          fontWeight: FontWeight.w600, // Same as original
                          color: Color(0xFF111418), // Same as original
                        ),
                      ),
                      const SizedBox(height: 4), // Same as original
                      const Text(
                        'Explore all insurance providers.',
                        style: TextStyle(
                          fontSize: 14, // Same as original
                          color: Color(0xFF637488), // Same as original
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShortcuts() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Shortcuts',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // TODO: Implement compare functionality
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF197FE5),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text(
                    'Compare',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // TODO: Implement view quotes functionality
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF197FE5),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text(
                    'View Quotes',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // TODO: Implement clients functionality
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF197FE5),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text(
                    'Clients',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Color(0xFFF0F2F4),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(Icons.home, 'Home', true),
              _buildNavItem(Icons.compare_arrows, 'Compare', false),
              _buildNavItem(Icons.people, 'Clients', false),
              _buildNavItem(Icons.description, 'Quotes', false),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, bool isActive) {
    return GestureDetector(
      onTap: () {
        // TODO: Implement navigation
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: isActive ? const Color(0xFFe92933) : const Color(0xFF637488),
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isActive ? const Color(0xFFe92933) : const Color(0xFF637488),
            ),
          ),
        ],
      ),
    );
  }
}


All Insurance Companies Page Design

> Design a mobile app page that displays a list/grid of **all insurance companies** in a clean, modern, and user-friendly layout. This page is accessible from a "Top Insurance Companies" section.
>
> what should page contain?

Each company should be displayed with the following:
> * A **search bar** at the top to search for a company by name
> * each company as a card just like the ones in the "alternate Top Insurance Companies" section with dropdown and sub cards. but dont include the sneak peak functiona and showing new ncompanies upon swiping. why not show new companies? because we will have all companies one below the other not just 3.
> * A **filter/sort icon** to sort alphabetically or by popularity
> * Fallback UI for no results found (in case of search/filter)
>
> 
> 🧭 **Navigation**:
>
> * Tapping on a company takes the user to a **Company Details Page** where all policies offered by that company are listed


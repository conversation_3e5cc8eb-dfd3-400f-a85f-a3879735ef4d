# Subscription Screen

A comprehensive subscription management page for the All About Insurance app, built based on the provided HTML design.

## Features

### Current Plan Status
- Displays current subscription status with active badge
- Shows Pro plan details with gradient background
- Next billing date information
- Action buttons for plan management and billing history

### Available Plans
- **Basic Plan** ($4.99/month): Perfect for getting started
  - Up to 5 projects
  - Basic templates
  - Community support
  - 1GB storage
  - Standard exports

- **Pro Plan** ($9.99/month): Best for professionals (Current Plan)
  - Unlimited projects
  - Premium templates
  - Priority support
  - 10GB storage
  - HD exports
  - Collaboration tools
  - Advanced analytics

- **Premium Plan** ($19.99/month): For power users and teams
  - Everything in Pro
  - Team collaboration
  - White-label exports
  - 100GB storage
  - 4K exports
  - API access
  - Custom integrations
  - 24/7 phone support

### Design Features
- Material 3 design principles
- Responsive layout with proper spacing
- Gradient backgrounds for plan cards
- Visual indicators for current and popular plans
- Smooth hover animations and transitions
- Consistent with app's design system

### Navigation
- Header with back button and help icon
- Integrated with app routing system
- Accessible from Settings and Profile sidebar

## Technical Implementation

### Components
- `SubscriptionScreen`: Main screen container
- `SubscriptionStatusCard`: Current plan status display
- `PlanCard`: Individual plan display with features

### Styling
- Background: `#f1f1f1` (matching app theme)
- Header height: 56px with bottom border
- Card styling: White background with transparency
- Gradients for plan types:
  - Basic: Grey gradient
  - Pro: Blue gradient (primary color)
  - Premium: Purple gradient

### State Management
- Uses Riverpod for state management
- Follows app's existing patterns
- Ready for integration with payment services

## Usage

### Navigation
Access subscription through:
1. Settings → Subscription
2. Profile sidebar → Subscription Plan
3. Direct navigation: `NavigationService.instance.navigateToSubscription()`

### Integration Points
- Ready for payment gateway integration
- Placeholder actions for plan selection
- TODO items for billing history and plan management

## Future Enhancements
- Payment gateway integration
- Billing history screen
- Plan upgrade/downgrade flows
- Subscription analytics
- Promotional offers and discounts

import 'dart:async';
import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';

class SessionManager {
  static SessionManager? _instance;
  static SessionManager get instance => _instance ??= SessionManager._();
  
  SessionManager._();
  
  static const String _userKey = 'current_user';
  static const String _sessionTokenKey = 'session_token';
  static const String _lastActivityKey = 'last_activity';
  static const String _loginAttemptsKey = 'login_attempts';
  static const String _lockoutTimeKey = 'lockout_time';
  static const String _biometricEnabledKey = 'biometric_enabled';
  
  // Session timeout duration (30 minutes)
  static const Duration sessionTimeout = Duration(minutes: 30);
  
  // Max login attempts before lockout
  static const int maxLoginAttempts = 5;
  
  // Lockout duration (15 minutes)
  static const Duration lockoutDuration = Duration(minutes: 15);
  
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  
  Timer? _sessionTimer;
  
  // Save user session
  Future<void> saveUserSession(AppUser user, [String? email]) async {
    try {
      final userJson = jsonEncode(user.toJson());
      await _secureStorage.write(key: _userKey, value: userJson);

      // Generate and save session token
      final sessionToken = _generateSessionToken();
      await _secureStorage.write(key: _sessionTokenKey, value: sessionToken);

      // Update last activity
      await _updateLastActivity();

      // Reset login attempts on successful login
      await _resetLoginAttempts(email ?? user.email);

      // Start session timer
      _startSessionTimer();
    } catch (e) {
      throw Exception('Failed to save user session: $e');
    }
  }
  
  // Get current user session
  Future<AppUser?> getCurrentUserSession() async {
    try {
      final userJson = await _secureStorage.read(key: _userKey);
      if (userJson == null) return null;
      
      // Check if session is still valid
      if (await _isSessionExpired()) {
        await clearSession();
        return null;
      }
      
      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      final user = AppUser.fromJson(userMap);
      
      // Update last activity
      await _updateLastActivity();
      
      // Restart session timer
      _startSessionTimer();
      
      return user;
    } catch (e) {
      // If there's an error reading the session, clear it
      await clearSession();
      return null;
    }
  }
  
  // Check if session is valid
  Future<bool> isSessionValid() async {
    try {
      final sessionToken = await _secureStorage.read(key: _sessionTokenKey);
      if (sessionToken == null) return false;
      
      return !(await _isSessionExpired());
    } catch (e) {
      return false;
    }
  }
  
  // Update last activity timestamp
  Future<void> updateActivity() async {
    await _updateLastActivity();
    _restartSessionTimer();
  }
  
  // Clear user session
  Future<void> clearSession() async {
    try {
      await Future.wait([
        _secureStorage.delete(key: _userKey),
        _secureStorage.delete(key: _sessionTokenKey),
        _secureStorage.delete(key: _lastActivityKey),
      ]);
      
      _cancelSessionTimer();
    } catch (e) {
      throw Exception('Failed to clear session: $e');
    }
  }
  
  // Brute force protection methods
  Future<bool> isAccountLocked([String? email]) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lockoutKey = email != null ? '${_lockoutTimeKey}_$email' : _lockoutTimeKey;
      final lockoutTimeString = prefs.getString(lockoutKey);

      if (lockoutTimeString == null) return false;

      final lockoutTime = DateTime.parse(lockoutTimeString);
      final now = DateTime.now();

      if (now.isAfter(lockoutTime)) {
        // Lockout period has expired, reset attempts
        await _resetLoginAttempts(email);
        await prefs.remove(lockoutKey);
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }
  
  Future<void> recordFailedLoginAttempt([String? email]) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final attemptsKey = email != null ? '${_loginAttemptsKey}_$email' : _loginAttemptsKey;
      final attempts = prefs.getInt(attemptsKey) ?? 0;
      final newAttempts = attempts + 1;

      await prefs.setInt(attemptsKey, newAttempts);

      if (newAttempts >= maxLoginAttempts) {
        // Lock the account
        final lockoutTime = DateTime.now().add(lockoutDuration);
        final lockoutKey = email != null ? '${_lockoutTimeKey}_$email' : _lockoutTimeKey;
        await prefs.setString(lockoutKey, lockoutTime.toIso8601String());
      }
    } catch (e) {
      // Handle error silently
    }
  }
  
  Future<int> getRemainingLoginAttempts([String? email]) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final attemptsKey = email != null ? '${_loginAttemptsKey}_$email' : _loginAttemptsKey;
      final attempts = prefs.getInt(attemptsKey) ?? 0;
      return (maxLoginAttempts - attempts).clamp(0, maxLoginAttempts);
    } catch (e) {
      return maxLoginAttempts;
    }
  }
  
  Future<Duration?> getRemainingLockoutTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lockoutTimeString = prefs.getString(_lockoutTimeKey);
      
      if (lockoutTimeString == null) return null;
      
      final lockoutTime = DateTime.parse(lockoutTimeString);
      final now = DateTime.now();
      
      if (now.isAfter(lockoutTime)) {
        return null;
      }
      
      return lockoutTime.difference(now);
    } catch (e) {
      return null;
    }
  }
  
  // Biometric authentication preferences
  Future<void> setBiometricEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_biometricEnabledKey, enabled);
  }
  
  Future<bool> isBiometricEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_biometricEnabledKey) ?? false;
  }
  
  // Private helper methods
  Future<void> _updateLastActivity() async {
    final now = DateTime.now().toIso8601String();
    await _secureStorage.write(key: _lastActivityKey, value: now);
  }
  
  Future<bool> _isSessionExpired() async {
    try {
      final lastActivityString = await _secureStorage.read(key: _lastActivityKey);
      if (lastActivityString == null) return true;
      
      final lastActivity = DateTime.parse(lastActivityString);
      final now = DateTime.now();
      
      return now.difference(lastActivity) > sessionTimeout;
    } catch (e) {
      return true;
    }
  }
  
  String _generateSessionToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecond;
    return '$timestamp-$random';
  }
  
  Future<void> _resetLoginAttempts([String? email]) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final attemptsKey = email != null ? '${_loginAttemptsKey}_$email' : _loginAttemptsKey;
      await prefs.remove(attemptsKey);
    } catch (e) {
      // Handle error silently
    }
  }
  
  void _startSessionTimer() {
    _cancelSessionTimer();
    _sessionTimer = Timer(sessionTimeout, () async {
      await clearSession();
    });
  }
  
  void _restartSessionTimer() {
    _startSessionTimer();
  }
  
  void _cancelSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = null;
  }
  
  // Cleanup method
  void dispose() {
    _cancelSessionTimer();
  }
}

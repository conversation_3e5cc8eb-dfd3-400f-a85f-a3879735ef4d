import 'package:flutter/material.dart';
import '../../services/navigation_service.dart';

class ProfileDemoScreen extends StatelessWidget {
  const ProfileDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFf1f1f1),
      appBar: AppBar(
        backgroundColor: const Color(0xFFf1f1f1),
        foregroundColor: const Color(0xFF111418),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFFe92933)),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Profile Screens Demo',
          style: TextStyle(
            color: Color(0xFF111418),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(
            height: 1,
            color: const Color(0x0F000000),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 32),
            
            // Header
            const Center(
              child: Column(
                children: [
                  Text(
                    'Profile Screens',
                    style: TextStyle(
                      color: Color(0xFF111418),
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Choose which profile screen to view',
                    style: TextStyle(
                      color: Color(0xFF637488),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 48),
            
            // Original Profile Screen Card
            _buildProfileCard(
              title: 'Original Profile Screen',
              description: 'Simple profile update screen with basic information fields',
              features: [
                'Basic personal information',
                'Simple form layout',
                'Standard validation',
              ],
              onTap: () {
                NavigationService.instance.navigateToProfile();
              },
              color: const Color(0xFF637488),
            ),
            
            const SizedBox(height: 24),
            
            // Enhanced Profile Screen Card
            _buildProfileCard(
              title: 'Enhanced Profile Screen',
              description: 'Comprehensive profile screen with advanced features and modern design',
              features: [
                'Profile picture upload',
                'Company logo upload',
                'Professional information',
                'Location & language settings',
                'Modern card-based design',
                'Advanced form validation',
              ],
              onTap: () {
                NavigationService.instance.navigateToEnhancedProfile();
              },
              color: const Color(0xFFe92933),
            ),
            
            const Spacer(),
            
            // Note
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFE3F2FD),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFF2196F3).withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Color(0xFF2196F3),
                    size: 20,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'The enhanced profile screen is based on the HTML design you provided and includes all the modern features and styling.',
                      style: TextStyle(
                        color: Color(0xFF1976D2),
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileCard({
    required String title,
    required String description,
    required List<String> features,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.person,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          color: color,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: const TextStyle(
                          color: Color(0xFF637488),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: color,
                  size: 16,
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Features:',
              style: TextStyle(
                color: Color(0xFF111418),
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            ...features.map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    feature,
                    style: const TextStyle(
                      color: Color(0xFF637488),
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
}

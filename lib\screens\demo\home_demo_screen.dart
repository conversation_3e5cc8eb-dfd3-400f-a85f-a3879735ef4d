import 'package:flutter/material.dart';
import '../../services/navigation_service.dart';

class HomeDemoScreen extends StatelessWidget {
  const HomeDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        title: const Text('Home Screen Demo'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF111418),
        elevation: 1,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.home,
              size: 80,
              color: Color(0xFF197FE5),
            ),
            const SizedBox(height: 32),
            const Text(
              'Home Screen Demo',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF111418),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Experience the home screen design',
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF637488),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 48),
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () {
                  NavigationService.instance.navigateToAlternateHome();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE92933),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  'Go to Home Screen',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 32),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFE0E7FF),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Column(
                children: [
                  Text(
                    '✨ New Features in Alternate Home',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF197FE5),
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Hero banner with referral program\n• Tabbed insurance categories\n• Top policies with expandable cards\n• Insurance company showcase\n• Quick action shortcuts\n• Modern bottom navigation',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF637488),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

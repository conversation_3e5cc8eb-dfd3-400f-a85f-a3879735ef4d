import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:formz/formz.dart';

import '../../models/auth_state.dart';
import '../../providers/auth_provider.dart';
import '../../services/navigation_service.dart';
import '../../widgets/auth/auth_text_field.dart';

class AlternateEmailSignupScreen extends ConsumerStatefulWidget {
  const AlternateEmailSignupScreen({super.key});

  @override
  ConsumerState<AlternateEmailSignupScreen> createState() => _AlternateEmailSignupScreenState();
}

class _AlternateEmailSignupScreenState extends ConsumerState<AlternateEmailSignupScreen> with RouteAware {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  Email _email = const Email.pure();
  Password _password = const Password.pure();

  bool _hasNavigatedToVerification = false;
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    // Clear fields when user returns from verification screen
    if (_hasNavigatedToVerification) {
      _clearFields();
      _hasNavigatedToVerification = false;
    }
  }

  void _clearFields() {
    _emailController.clear();
    _passwordController.clear();
    setState(() {
      _email = const Email.pure();
      _password = const Password.pure();

    });
  }

  void _onEmailChanged(String value) {
    setState(() {
      _email = Email.dirty(value);
      _validateForm();
    });
  }

  void _onPasswordChanged(String value) {
    setState(() {
      _password = Password.dirty(value);
      _validateForm();
    });
  }

  void _validateForm() {
    // Form validation is handled by the form key
    Formz.validate([_email, _password]);
  }

  void _onRegisterPressed() {
    if (_formKey.currentState?.validate() ?? false) {
      ref.read(authProvider.notifier).createUserWithEmailAndPassword(
        email: _emailController.text,
        password: _passwordController.text,
        displayName: null,
      );
    }
  }

  void _onTermsPressed() {
    NavigationService.instance.navigateToTerms();
  }

  void _onPrivacyPressed() {
    NavigationService.instance.navigateToPolicy();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Listen to auth state changes
    ref.listen<AuthState>(authProvider, (previous, next) {
      if (next.status == AuthStatus.error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Center(
              child: Text(
                next.errorMessage ?? 'An error occurred',
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      } else if (next.status == AuthStatus.verificationPending) {
        // Only navigate if we haven't already navigated to verification
        // and we're not already on a verification screen
        if (!_hasNavigatedToVerification && mounted) {
          final currentRoute = ModalRoute.of(context)?.settings.name;
          final isOnVerificationScreen = currentRoute == '/alternate-email-verification' ||
                                       currentRoute == '/email-verification';

          if (!isOnVerificationScreen) {
            // Mark that user is navigating to verification
            _hasNavigatedToVerification = true;
            // Navigate to alternate email verification screen
            WidgetsBinding.instance.addPostFrameCallback((_) {
              NavigationService.instance.navigateToAlternateEmailVerification(
                email: _emailController.text,
                password: _passwordController.text,
              );
            });
          }
        }
      } else if (next.status == AuthStatus.authenticated) {
        // This shouldn't happen in the new flow - user should go through verification
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Main Content - Centered
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 400),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Title
                        _buildTitle(),

                        const SizedBox(height: 40),
                        
                        // Email Field
                        _buildEmailField(),
                        
                        const SizedBox(height: 24),
                        
                        // Password Field
                        _buildPasswordField(),

                        // Password Strength Indicator
                        if (_passwordController.text.isNotEmpty)
                          PasswordStrengthIndicator(password: _passwordController.text),

                        const SizedBox(height: 32),
                        
                        // Sign Up Button
                        _buildSignUpButton(authState),
                        
                        const SizedBox(height: 24),
                        
                        // Terms Text
                        _buildTermsText(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            
            // Header - Positioned at top
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: _buildHeader(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          IconButton(
            onPressed: () => NavigationService.instance.goBack(),
            icon: const Icon(
              Icons.arrow_back,
              color: Color(0xFFe92933),
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        const Text(
          'Create Account',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color(0xFF1a1a1a),
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'All About Insurance',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color(0xFFe92933),
          ),
        ),
        const SizedBox(height: 8),

      ],
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      style: const TextStyle(
        fontSize: 16,
        color: Color(0xFF1a1a1a),
      ),
      decoration: InputDecoration(
        labelText: 'Email',
        hintText: 'Email address',
        labelStyle: const TextStyle(
          color: Color(0xFF666666),
          fontSize: 14,
        ),
        hintStyle: const TextStyle(
          color: Color(0xFF666666),
          fontSize: 16,
        ),
        filled: true,
        fillColor: const Color(0xFFf2f2f2),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF666666),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF666666),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFe92933),
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Email is required';
        }
        final email = Email.dirty(value.trim());
        if (email.error == EmailValidationError.invalid) {
          return 'Please enter a valid email address';
        }
        return null;
      },
      onChanged: _onEmailChanged,
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      style: const TextStyle(
        fontSize: 16,
        color: Color(0xFF1a1a1a),
      ),
      decoration: InputDecoration(
        labelText: 'Password',
        hintText: 'Password',
        labelStyle: const TextStyle(
          color: Color(0xFF666666),
          fontSize: 14,
        ),
        hintStyle: const TextStyle(
          color: Color(0xFF666666),
          fontSize: 16,
        ),
        filled: true,
        fillColor: const Color(0xFFf2f2f2),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF666666),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF666666),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFFe92933),
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        suffixIcon: IconButton(
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
          icon: Icon(
            _obscurePassword ? Icons.visibility : Icons.visibility_off,
            color: const Color(0xFF666666),
          ),
        ),
      ),
      validator: (value) {
        final password = Password.dirty(value ?? '');
        if (password.error == PasswordValidationError.empty) {
          return 'Password is required';
        }
        if (password.error == PasswordValidationError.tooShort) {
          return 'Password must be at least 6 characters';
        }
        if (password.error == PasswordValidationError.weak) {
          return 'Password must contain letters and numbers';
        }
        return null;
      },
      onChanged: _onPasswordChanged,
    );
  }

  Widget _buildSignUpButton(AuthState authState) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: authState.isLoading ? null : _onRegisterPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFe92933),
          foregroundColor: Colors.white,
          disabledBackgroundColor: const Color(0xFFe92933), // Keep red when disabled
          disabledForegroundColor: Colors.white, // Keep white text when disabled
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: authState.isLoading
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'Sign Up',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ],
              )
            : const Text(
                'Sign Up',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Widget _buildTermsText() {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        style: const TextStyle(
          fontSize: 12,
          color: Color(0xFF666666),
        ),
        children: [
          const TextSpan(text: 'By creating an account, you agree to our '),
          TextSpan(
            text: 'Terms of Service',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Color(0xFFe92933),
            ),
            recognizer: TapGestureRecognizer()..onTap = _onTermsPressed,
          ),
          const TextSpan(text: ' and '),
          TextSpan(
            text: 'Privacy Policy',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Color(0xFFe92933),
            ),
            recognizer: TapGestureRecognizer()..onTap = _onPrivacyPressed,
          ),
          const TextSpan(text: '.'),
        ],
      ),
    );
  }
}

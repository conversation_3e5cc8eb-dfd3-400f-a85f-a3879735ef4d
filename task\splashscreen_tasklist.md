# Splash Screen & Onboarding Implementation

## Parent Task: Implement Splash Screen and Onboarding Flow

### Subtasks
- [ ] Set up splash screen package
  - [ ] Add `flutter_native_splash` dependency to `pubspec.yaml`
  - [ ] Configure splash screen design (logo, background color, etc.)
  - [ ] Generate splash screen assets for both Android and iOS

- [ ] Create onboarding screens
  - [ ] Design 3-4 intro screens with images and text
  - [ ] Implement swipe navigation between screens
  - [ ] Add skip and done buttons
  - [ ] Add page indicators (dots)

- [ ] Implement state management for first-time users
  - [ ] Add `shared_preferences` or `hive` package
  - [ ] Create service to handle onboarding completion status
  - [ ] Implement logic to show onboarding only on first launch

- [ ] Navigation flow
  - [ ] Set up initial route to show splash screen
  - [ ] Add logic to navigate from splash to onboarding or main app
  - [ ] Implement smooth transitions between screens

- [ ] Testing
  - [ ] Test splash screen visibility and timing
  - [ ] Verify onboarding flow works correctly
  - [ ] Test first-time vs returning user scenarios
  - [ ] Test on both Android and iOS

### Relevant Files
- `pubspec.yaml` - For adding dependencies
- `lib/main.dart` - Main app configuration
- `lib/screens/splash_screen.dart` - Splash screen implementation
- `lib/screens/onboarding/` - Directory for onboarding screens
- `lib/services/onboarding_service.dart` - Service to handle onboarding state
- `android/app/src/main/res/` - Android splash screen assets
- `ios/Runner/Assets.xcassets/` - iOS splash screen assets

### Notes
- Follow Material Design 3 guidelines for the UI
- Ensure smooth animations between screens
- Make sure the splash screen looks good on all device sizes
- Consider adding accessibility features (e.g., screen reader support)
- Test on both light and dark themes

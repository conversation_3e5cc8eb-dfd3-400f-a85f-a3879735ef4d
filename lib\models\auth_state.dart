import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';
import 'user_model.dart';

enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  verificationPending,
  error,
}

class AuthState extends Equatable {
  final AuthStatus status;
  final AppUser? user;
  final String? errorMessage;
  final bool isLoading;

  const AuthState({
    required this.status,
    this.user,
    this.errorMessage,
    this.isLoading = false,
  });

  const AuthState.initial()
      : status = AuthStatus.initial,
        user = null,
        errorMessage = null,
        isLoading = false;

  const AuthState.loading()
      : status = AuthStatus.loading,
        user = null,
        errorMessage = null,
        isLoading = true;

  const AuthState.authenticated(this.user)
      : status = AuthStatus.authenticated,
        errorMessage = null,
        isLoading = false;

  const AuthState.unauthenticated()
      : status = AuthStatus.unauthenticated,
        user = null,
        errorMessage = null,
        isLoading = false;

  const AuthState.verificationPending()
      : status = AuthStatus.verificationPending,
        user = null,
        errorMessage = null,
        isLoading = false;

  const AuthState.error(String message)
      : status = AuthStatus.error,
        user = null,
        errorMessage = message,
        isLoading = false;

  AuthState copyWith({
    AuthStatus? status,
    AppUser? user,
    String? errorMessage,
    bool? isLoading,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  @override
  List<Object?> get props => [status, user, errorMessage, isLoading];
}

enum EmailValidationError { empty, invalid }

class Email extends FormzInput<String, EmailValidationError> {
  const Email.pure() : super.pure('');
  const Email.dirty([super.value = '']) : super.dirty();

  static final RegExp _emailRegExp = RegExp(
    r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$',
  );

  @override
  EmailValidationError? validator(String? value) {
    if (value?.isEmpty ?? true) return EmailValidationError.empty;
    return _emailRegExp.hasMatch(value!) ? null : EmailValidationError.invalid;
  }
}

enum PasswordValidationError { empty, tooShort, weak }

class Password extends FormzInput<String, PasswordValidationError> {
  const Password.pure() : super.pure('');
  const Password.dirty([super.value = '']) : super.dirty();

  @override
  PasswordValidationError? validator(String? value) {
    if (value?.isEmpty ?? true) return PasswordValidationError.empty;
    if (value!.length < 6) return PasswordValidationError.tooShort;
    
    // Check for weak password (should contain at least one letter and one number)
    final hasLetter = RegExp(r'[a-zA-Z]').hasMatch(value);
    final hasNumber = RegExp(r'[0-9]').hasMatch(value);
    
    if (!hasLetter || !hasNumber) return PasswordValidationError.weak;
    
    return null;
  }
}

enum ConfirmPasswordValidationError { empty, mismatch }

class ConfirmPassword extends FormzInput<String, ConfirmPasswordValidationError> {
  final String password;
  
  const ConfirmPassword.pure({this.password = ''}) : super.pure('');
  const ConfirmPassword.dirty({required this.password, String value = ''}) : super.dirty(value);

  @override
  ConfirmPasswordValidationError? validator(String? value) {
    if (value?.isEmpty ?? true) return ConfirmPasswordValidationError.empty;
    return value == password ? null : ConfirmPasswordValidationError.mismatch;
  }
}

enum PhoneValidationError { empty, invalid }

class PhoneNumber extends FormzInput<String, PhoneValidationError> {
  const PhoneNumber.pure() : super.pure('');
  const PhoneNumber.dirty([super.value = '']) : super.dirty();

  static final RegExp _phoneRegExp = RegExp(r'^\+?[1-9]\d{1,14}$');

  @override
  PhoneValidationError? validator(String? value) {
    if (value?.isEmpty ?? true) return PhoneValidationError.empty;
    return _phoneRegExp.hasMatch(value!) ? null : PhoneValidationError.invalid;
  }
}

enum OtpValidationError { empty, invalid }

class OtpCode extends FormzInput<String, OtpValidationError> {
  const OtpCode.pure() : super.pure('');
  const OtpCode.dirty([super.value = '']) : super.dirty();

  @override
  OtpValidationError? validator(String? value) {
    if (value?.isEmpty ?? true) return OtpValidationError.empty;
    if (value!.length != 6) return OtpValidationError.invalid;
    if (!RegExp(r'^\d{6}$').hasMatch(value)) return OtpValidationError.invalid;
    return null;
  }
}

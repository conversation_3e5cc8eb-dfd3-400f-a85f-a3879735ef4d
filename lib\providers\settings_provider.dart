import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/settings_model.dart';

class SettingsNotifier extends StateNotifier<SettingsModel> {
  SettingsNotifier() : super(const SettingsModel()) {
    _loadSettings();
  }

  static const String _pushNotificationsKey = 'push_notifications';
  static const String _emailNotificationsKey = 'email_notifications';
  static const String _locationServicesKey = 'location_services';
  static const String _analyticsKey = 'analytics';
  static const String _darkModeKey = 'dark_mode';
  static const String _biometricKey = 'biometric';
  static const String _autoDownloadKey = 'auto_download';

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      state = SettingsModel(
        pushNotificationsEnabled: prefs.getBool(_pushNotificationsKey) ?? true,
        emailNotificationsEnabled: prefs.getBool(_emailNotificationsKey) ?? false,
        locationServicesEnabled: prefs.getBool(_locationServicesKey) ?? true,
        analyticsEnabled: prefs.getBool(_analyticsKey) ?? false,
        darkModeEnabled: prefs.getBool(_darkModeKey) ?? false,
        biometricEnabled: prefs.getBool(_biometricKey) ?? true,
        autoDownloadEnabled: prefs.getBool(_autoDownloadKey) ?? false,
      );
    } catch (e) {
      // If loading fails, keep default values
      print('Error loading settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool(_pushNotificationsKey, state.pushNotificationsEnabled);
      await prefs.setBool(_emailNotificationsKey, state.emailNotificationsEnabled);
      await prefs.setBool(_locationServicesKey, state.locationServicesEnabled);
      await prefs.setBool(_analyticsKey, state.analyticsEnabled);
      await prefs.setBool(_darkModeKey, state.darkModeEnabled);
      await prefs.setBool(_biometricKey, state.biometricEnabled);
      await prefs.setBool(_autoDownloadKey, state.autoDownloadEnabled);
    } catch (e) {
      print('Error saving settings: $e');
    }
  }

  Future<void> setPushNotifications(bool enabled) async {
    state = state.copyWith(pushNotificationsEnabled: enabled);
    await _saveSettings();
  }

  Future<void> setEmailNotifications(bool enabled) async {
    state = state.copyWith(emailNotificationsEnabled: enabled);
    await _saveSettings();
  }

  Future<void> setLocationServices(bool enabled) async {
    state = state.copyWith(locationServicesEnabled: enabled);
    await _saveSettings();
  }

  Future<void> setAnalytics(bool enabled) async {
    state = state.copyWith(analyticsEnabled: enabled);
    await _saveSettings();
  }

  Future<void> setDarkMode(bool enabled) async {
    state = state.copyWith(darkModeEnabled: enabled);
    await _saveSettings();
  }

  Future<void> setBiometric(bool enabled) async {
    state = state.copyWith(biometricEnabled: enabled);
    await _saveSettings();
  }

  Future<void> setAutoDownload(bool enabled) async {
    state = state.copyWith(autoDownloadEnabled: enabled);
    await _saveSettings();
  }

  Future<void> resetToDefaults() async {
    state = const SettingsModel();
    await _saveSettings();
  }
}

final settingsProvider = StateNotifierProvider<SettingsNotifier, SettingsModel>((ref) {
  return SettingsNotifier();
});

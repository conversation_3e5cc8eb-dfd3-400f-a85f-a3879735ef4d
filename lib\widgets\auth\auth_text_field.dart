import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AuthTextField extends StatefulWidget {
  final String label;
  final String? hintText;
  final TextEditingController controller;
  final String? Function(String?)? validator;
  final bool obscureText;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool enabled;
  final int? maxLength;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final bool autofocus;
  final String? errorText;
  final bool showPasswordToggle;

  const AuthTextField({
    super.key,
    required this.label,
    this.hintText,
    required this.controller,
    this.validator,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.inputFormatters,
    this.prefixIcon,
    this.suffixIcon,
    this.enabled = true,
    this.maxLength,
    this.onTap,
    this.onChanged,
    this.autofocus = false,
    this.errorText,
    this.showPasswordToggle = false,
  });

  @override
  State<AuthTextField> createState() => _AuthTextFieldState();
}

class _AuthTextFieldState extends State<AuthTextField> {
  bool _obscureText = false;

  @override
  void initState() {
    super.initState();
    _obscureText = widget.obscureText;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: theme.textTheme.labelLarge?.copyWith(
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
            controller: widget.controller,
            validator: widget.validator,
            obscureText: _obscureText,
            keyboardType: widget.keyboardType,
            inputFormatters: widget.inputFormatters,
            enabled: widget.enabled,
            maxLength: widget.maxLength,
            onTap: widget.onTap,
            onChanged: widget.onChanged,
            autofocus: widget.autofocus,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.black,
            ),
            cursorColor: Colors.black,
            decoration: InputDecoration(
              hintText: widget.hintText,
              hintStyle: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
              prefixIcon: widget.prefixIcon,
              suffixIcon: widget.showPasswordToggle && widget.obscureText
                  ? IconButton(
                      icon: Icon(
                        _obscureText ? Icons.visibility : Icons.visibility_off,
                        color: colorScheme.onSurfaceVariant,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureText = !_obscureText;
                        });
                      },
                    )
                  : widget.suffixIcon,
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colorScheme.outline,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colorScheme.outline,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colorScheme.outline,
                  width: 1,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colorScheme.error,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colorScheme.error,
                  width: 2,
                ),
              ),
              errorText: widget.errorText,
              errorStyle: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.error,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              counterText: '',
            ),
          ),
      ],
    );
  }
}

class PasswordStrengthIndicator extends StatefulWidget {
  final String password;

  const PasswordStrengthIndicator({
    super.key,
    required this.password,
  });

  @override
  State<PasswordStrengthIndicator> createState() => _PasswordStrengthIndicatorState();
}

class _PasswordStrengthIndicatorState extends State<PasswordStrengthIndicator> {
  bool _showDetails = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final strength = _calculatePasswordStrength(widget.password);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: strength.value,
                backgroundColor: colorScheme.surface,
                valueColor: AlwaysStoppedAnimation<Color>(strength.color),
                minHeight: 4,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              strength.label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: strength.color,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () {
                setState(() {
                  _showDetails = !_showDetails;
                });
              },
              child: Icon(
                Icons.info_outline,
                size: 16,
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        if (_showDetails && widget.password.isNotEmpty) ...[
          const SizedBox(height: 8),
          ...strength.requirements.map((req) => Padding(
                padding: const EdgeInsets.only(top: 2),
                child: Row(
                  children: [
                    Icon(
                      req.isMet ? Icons.check_circle : Icons.circle_outlined,
                      size: 16,
                      color: req.isMet ? const Color(0xFF008a59) : Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      req.text,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: req.isMet ? const Color(0xFF008a59) : Colors.red,
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ],
    );
  }

  PasswordStrength _calculatePasswordStrength(String password) {
    if (password.isEmpty) {
      return PasswordStrength(
        value: 0.0,
        label: 'Enter password',
        color: Colors.grey,
        requirements: _getRequirements(password),
      );
    }

    int score = 0;
    final requirements = _getRequirements(password);
    
    for (final req in requirements) {
      if (req.isMet) score++;
    }

    if (score <= 2) {
      return PasswordStrength(
        value: 0.25,
        label: 'Weak',
        color: Colors.red,
        requirements: requirements,
      );
    } else if (score <= 4) {
      return PasswordStrength(
        value: 0.5,
        label: 'Fair',
        color: Colors.orange,
        requirements: requirements,
      );
    } else if (score <= 5) {
      return PasswordStrength(
        value: 0.75,
        label: 'Good',
        color: const Color(0xFF008a59),
        requirements: requirements,
      );
    } else {
      return PasswordStrength(
        value: 1.0,
        label: 'Strong',
        color: Colors.green,
        requirements: requirements,
      );
    }
  }

  List<PasswordRequirement> _getRequirements(String password) {
    return [
      PasswordRequirement(
        text: 'At least 8 characters',
        isMet: password.length >= 8,
      ),
      PasswordRequirement(
        text: 'Contains uppercase letter',
        isMet: RegExp(r'[A-Z]').hasMatch(password),
      ),
      PasswordRequirement(
        text: 'Contains lowercase letter',
        isMet: RegExp(r'[a-z]').hasMatch(password),
      ),
      PasswordRequirement(
        text: 'Contains number',
        isMet: RegExp(r'[0-9]').hasMatch(password),
      ),
      PasswordRequirement(
        text: 'Contains special character',
        isMet: RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password),
      ),
    ];
  }
}

class PasswordStrength {
  final double value;
  final String label;
  final Color color;
  final List<PasswordRequirement> requirements;

  PasswordStrength({
    required this.value,
    required this.label,
    required this.color,
    required this.requirements,
  });
}

class PasswordRequirement {
  final String text;
  final bool isMet;

  PasswordRequirement({
    required this.text,
    required this.isMet,
  });
}

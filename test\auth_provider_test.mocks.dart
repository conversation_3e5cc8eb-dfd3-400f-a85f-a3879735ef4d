// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in aai/test/auth_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:aai/models/user_model.dart' as _i2;
import 'package:aai/services/auth_service.dart' as _i3;
import 'package:aai/services/session_manager.dart' as _i6;
import 'package:firebase_auth/firebase_auth.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAppUser_0 extends _i1.SmartFake implements _i2.AppUser {
  _FakeAppUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i3.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.AppUser?> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i4.Stream<_i2.AppUser?>.empty(),
          )
          as _i4.Stream<_i2.AppUser?>);

  @override
  _i4.Stream<_i5.User?> get firebaseAuthStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#firebaseAuthStateChanges),
            returnValue: _i4.Stream<_i5.User?>.empty(),
          )
          as _i4.Stream<_i5.User?>);

  @override
  _i4.Future<_i2.AppUser> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i4.Future<_i2.AppUser>.value(
              _FakeAppUser_0(
                this,
                Invocation.method(#signInWithEmailAndPassword, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.AppUser>);

  @override
  _i4.Future<_i2.AppUser> createUserWithEmailAndPassword({
    required String? email,
    required String? password,
    String? displayName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createUserWithEmailAndPassword, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
            }),
            returnValue: _i4.Future<_i2.AppUser>.value(
              _FakeAppUser_0(
                this,
                Invocation.method(#createUserWithEmailAndPassword, [], {
                  #email: email,
                  #password: password,
                  #displayName: displayName,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.AppUser>);

  @override
  _i4.Future<void> createUserAccountWithEmailVerification({
    required String? email,
    required String? password,
    String? displayName,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createUserAccountWithEmailVerification, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> resendEmailVerification(String? email, String? password) =>
      (super.noSuchMethod(
            Invocation.method(#resendEmailVerification, [email, password]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.AppUser> authenticateAfterVerification({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#authenticateAfterVerification, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i4.Future<_i2.AppUser>.value(
              _FakeAppUser_0(
                this,
                Invocation.method(#authenticateAfterVerification, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.AppUser>);

  @override
  _i4.Future<_i2.AppUser> signInWithGoogle() =>
      (super.noSuchMethod(
            Invocation.method(#signInWithGoogle, []),
            returnValue: _i4.Future<_i2.AppUser>.value(
              _FakeAppUser_0(this, Invocation.method(#signInWithGoogle, [])),
            ),
          )
          as _i4.Future<_i2.AppUser>);

  @override
  _i4.Future<void> verifyPhoneNumber({
    required String phoneNumber,
    required dynamic Function(String) onCodeSent,
    required dynamic Function(String) onError,
    dynamic Function(_i2.AppUser)? onAutoVerified,
    int? forceResendingToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #phoneNumber: phoneNumber,
              #onCodeSent: onCodeSent,
              #onError: onError,
              #onAutoVerified: onAutoVerified,
              #forceResendingToken: forceResendingToken,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.AppUser> signInWithPhoneOtp({
    required String? verificationId,
    required String? otpCode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneOtp, [], {
              #verificationId: verificationId,
              #otpCode: otpCode,
            }),
            returnValue: _i4.Future<_i2.AppUser>.value(
              _FakeAppUser_0(
                this,
                Invocation.method(#signInWithPhoneOtp, [], {
                  #verificationId: verificationId,
                  #otpCode: otpCode,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.AppUser>);

  @override
  _i4.Future<void> sendPasswordResetEmail(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [email]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> sendEmailVerification() =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> reloadUser() =>
      (super.noSuchMethod(
            Invocation.method(#reloadUser, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deleteAccount() =>
      (super.noSuchMethod(
            Invocation.method(#deleteAccount, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}

/// A class which mocks [SessionManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockSessionManager extends _i1.Mock implements _i6.SessionManager {
  MockSessionManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> saveUserSession(_i2.AppUser? user, [String? email]) =>
      (super.noSuchMethod(
            Invocation.method(#saveUserSession, [user, email]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.AppUser?> getCurrentUserSession() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUserSession, []),
            returnValue: _i4.Future<_i2.AppUser?>.value(),
          )
          as _i4.Future<_i2.AppUser?>);

  @override
  _i4.Future<bool> isSessionValid() =>
      (super.noSuchMethod(
            Invocation.method(#isSessionValid, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> updateActivity() =>
      (super.noSuchMethod(
            Invocation.method(#updateActivity, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> clearSession() =>
      (super.noSuchMethod(
            Invocation.method(#clearSession, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> isAccountLocked([String? email]) =>
      (super.noSuchMethod(
            Invocation.method(#isAccountLocked, [email]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> recordFailedLoginAttempt([String? email]) =>
      (super.noSuchMethod(
            Invocation.method(#recordFailedLoginAttempt, [email]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<int> getRemainingLoginAttempts([String? email]) =>
      (super.noSuchMethod(
            Invocation.method(#getRemainingLoginAttempts, [email]),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);

  @override
  _i4.Future<Duration?> getRemainingLockoutTime() =>
      (super.noSuchMethod(
            Invocation.method(#getRemainingLockoutTime, []),
            returnValue: _i4.Future<Duration?>.value(),
          )
          as _i4.Future<Duration?>);

  @override
  _i4.Future<void> setBiometricEnabled(bool? enabled) =>
      (super.noSuchMethod(
            Invocation.method(#setBiometricEnabled, [enabled]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> isBiometricEnabled() =>
      (super.noSuchMethod(
            Invocation.method(#isBiometricEnabled, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

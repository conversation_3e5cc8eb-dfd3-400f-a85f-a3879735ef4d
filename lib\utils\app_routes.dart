import 'package:flutter/material.dart';
import '../screens/splash/splash_screen.dart';
import '../screens/onboarding/onboarding_screen.dart';
import '../screens/auth/alternate_sign_in_screen.dart';
import '../screens/auth/otp_screen.dart';
import '../screens/auth/alternate_login_screen.dart';
import '../screens/auth/alternate_register_screen.dart';
import '../screens/auth/alternate_forgot_password_screen.dart';
import '../screens/auth/alternate_email_signup_screen.dart';
import '../screens/auth/alternate_email_verification_screen.dart';
import '../screens/auth/phone_auth_screen.dart';
import '../screens/home/<USER>';
import '../screens/demo/home_demo_screen.dart';
import '../screens/profile/profile_update_screen.dart';
import '../screens/subscription/subscription_screen.dart';
import '../screens/profile/enhanced_profile_screen.dart';
import '../screens/demo/profile_demo_screen.dart';
import '../screens/notifications/notifications_screen.dart';
import '../screens/settings/settings_screen.dart';
import '../screens/legal/terms_screen.dart';
import '../screens/legal/policy_screen.dart';
import '../screens/referral_screen.dart';
import '../screens/feedback_screen.dart';
import '../screens/faq_screen.dart';
import '../screens/products/product_details_screen.dart';
import '../services/navigation_service.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String alternateSignIn = '/alternate-sign-in';
  static const String otp = '/otp';
  static const String alternateLogin = '/alternate-login';
  static const String alternateRegister = '/alternate-register';
  static const String alternateForgotPassword = '/alternate-forgot-password';
  static const String alternateEmailSignup = '/alternate-email-signup';
  static const String alternateEmailVerification = '/alternate-email-verification';
  static const String phoneAuth = '/phone-auth';
  static const String home = '/home';
  static const String alternateHome = '/alternate-home';
  static const String homeDemo = '/home-demo';
  static const String profileDemo = '/profile-demo';
  static const String profile = '/profile';
  static const String enhancedProfile = '/enhanced-profile';
  static const String notifications = '/notifications';
  static const String settings = '/settings';
  static const String subscription = '/subscription';
  static const String terms = '/terms';
  static const String policy = '/policy';
  static const String referral = '/referral';
  static const String feedback = '/feedback';
  static const String faq = '/faq';
  static const String productDetails = '/product-details';

  // Route generator
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(
          builder: (_) => SplashScreen(
            onboardingBuilder: () => OnboardingScreen(
              onComplete: () => NavigationService.instance.navigateToAlternateSignIn(),
            ),
            homeBuilder: () => const AlternateHomeScreen(),
            onNavigateToOnboarding: () => NavigationService.instance.navigateToOnboarding(),
            onNavigateToAuth: () => NavigationService.instance.navigateToAlternateSignIn(),
          ),
          settings: settings,
        );

      case onboarding:
        return MaterialPageRoute(
          builder: (_) => OnboardingScreen(
            onComplete: () => NavigationService.instance.navigateToAlternateSignIn(),
          ),
          settings: settings,
        );

      case alternateSignIn:
        return MaterialPageRoute(
          builder: (_) => const AlternateSignInScreen(),
          settings: settings,
        );

      case otp:
        final args = settings.arguments as Map<String, dynamic>?;
        final phoneNumber = args?['phoneNumber'] as String? ?? '';
        final verificationId = args?['verificationId'] as String?;
        return MaterialPageRoute(
          builder: (_) => OtpScreen(
            phoneNumber: phoneNumber,
            verificationId: verificationId,
          ),
          settings: settings,
        );

      case alternateLogin:
        return MaterialPageRoute(
          builder: (_) => const AlternateLoginScreen(),
          settings: settings,
        );

      case alternateRegister:
        return MaterialPageRoute(
          builder: (_) => const AlternateRegisterScreen(),
          settings: settings,
        );

      case alternateForgotPassword:
        return MaterialPageRoute(
          builder: (_) => const AlternateForgotPasswordScreen(),
          settings: settings,
        );

      case alternateEmailSignup:
        return MaterialPageRoute(
          builder: (_) => const AlternateEmailSignupScreen(),
          settings: settings,
        );

      case alternateEmailVerification:
        final args = settings.arguments as Map<String, String>?;
        return MaterialPageRoute(
          builder: (_) => AlternateEmailVerificationScreen(
            email: args?['email'] ?? '',
            password: args?['password'] ?? '',
          ),
          settings: settings,
        );

      case phoneAuth:
        return MaterialPageRoute(
          builder: (_) => const PhoneAuthScreen(),
          settings: settings,
        );

      case home:
        return MaterialPageRoute(
          builder: (_) => const AlternateHomeScreen(),
          settings: settings,
        );

      case alternateHome:
        return MaterialPageRoute(
          builder: (_) => const AlternateHomeScreen(),
          settings: settings,
        );

      case homeDemo:
        return MaterialPageRoute(
          builder: (_) => const HomeDemoScreen(),
          settings: settings,
        );

      case profileDemo:
        return MaterialPageRoute(
          builder: (_) => const ProfileDemoScreen(),
          settings: settings,
        );

      case profile:
        return MaterialPageRoute(
          builder: (_) => const ProfileUpdateScreen(),
          settings: settings,
        );

      case enhancedProfile:
        return MaterialPageRoute(
          builder: (_) => const EnhancedProfileScreen(),
          settings: settings,
        );

      case notifications:
        return MaterialPageRoute(
          builder: (_) => const NotificationsScreen(),
          settings: settings,
        );

      case AppRoutes.settings:
        return MaterialPageRoute(
          builder: (_) => const SettingsScreen(),
          settings: settings,
        );

      case subscription:
        return MaterialPageRoute(
          builder: (_) => const SubscriptionScreen(),
          settings: settings,
        );

      case terms:
        return MaterialPageRoute(
          builder: (_) => const TermsScreen(),
          settings: settings,
        );

      case policy:
        return MaterialPageRoute(
          builder: (_) => const PolicyScreen(),
          settings: settings,
        );

      case referral:
        return MaterialPageRoute(
          builder: (_) => const ReferralScreen(),
          settings: settings,
        );

      case feedback:
        return MaterialPageRoute(
          builder: (_) => const FeedbackScreen(),
          settings: settings,
        );

      case faq:
        return MaterialPageRoute(
          builder: (_) => const FAQScreen(),
          settings: settings,
        );

      case productDetails:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => ProductDetailsScreen(
            productName: args?['productName'] ?? '',
            productDescription: args?['productDescription'] ?? '',
            companyName: args?['companyName'] ?? '',
            companyId: args?['companyId'] ?? '',
          ),
          settings: settings,
        );

      default:
        return MaterialPageRoute(
          builder: (_) => const AlternateSignInScreen(), // Fallback to alternate sign-in
          settings: settings,
        );
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import '../../services/navigation_service.dart';
import '../../providers/auth_provider.dart';
import '../../models/auth_state.dart';

class OtpScreen extends ConsumerStatefulWidget {
  final String phoneNumber;
  final String? verificationId;

  const OtpScreen({
    super.key,
    required this.phoneNumber,
    this.verificationId,
  });

  @override
  ConsumerState<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends ConsumerState<OtpScreen> {
  final List<TextEditingController> _controllers = List.generate(6, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(6, (index) => FocusNode());

  Timer? _timer;
  int _resendCountdown = 59;
  bool _canResend = false;
  String? _currentVerificationId;
  bool _isVerifying = false;
  
  @override
  void initState() {
    super.initState();
    _currentVerificationId = widget.verificationId;
    _startResendTimer();
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }
  
  void _startResendTimer() {
    _canResend = false;
    _resendCountdown = 59;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_resendCountdown > 0) {
          _resendCountdown--;
        } else {
          _canResend = true;
          timer.cancel();
        }
      });
    });
  }
  
  void _onOtpChanged(String value, int index) {
    if (value.isNotEmpty && index < 5) {
      _focusNodes[index + 1].requestFocus();
    } else if (value.isEmpty && index > 0) {
      _focusNodes[index - 1].requestFocus();
    }
  }
  
  String _getOtpCode() {
    return _controllers.map((controller) => controller.text).join();
  }
  
  bool _isOtpComplete() {
    return _getOtpCode().length == 6;
  }
  
  void _onVerifyPressed() async {
    if (!_isOtpComplete()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Center(
            child: Text(
              'Please enter complete OTP',
              style: TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    if (_currentVerificationId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Center(
            child: Text(
              'Verification session expired. Please try again.',
              style: TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    setState(() {
      _isVerifying = true;
    });

    try {
      // Verify OTP with Firebase
      await ref.read(authProvider.notifier).signInWithPhoneOtp(
        verificationId: _currentVerificationId!,
        otpCode: _getOtpCode(),
      );

      // Navigation will be handled by auth state listener
    } catch (e) {
      // Error handling will be done by auth provider
    } finally {
      if (mounted) {
        setState(() {
          _isVerifying = false;
        });
      }
    }
  }
  
  void _onResendPressed() async {
    if (!_canResend) return;

    // Extract phone number from display format (+91 12345 67890) to Firebase format (+911234567890)
    final phoneNumber = widget.phoneNumber.replaceAll(' ', '');

    // Call Firebase to resend OTP
    ref.read(authProvider.notifier).verifyPhoneNumber(
      phoneNumber: phoneNumber,
      onCodeSent: (verificationId) {
        setState(() {
          _currentVerificationId = verificationId;
        });
        _startResendTimer();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Center(
              child: Text(
                'OTP has been resent',
                style: TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
            backgroundColor: Colors.blue,
            behavior: SnackBarBehavior.floating,
          ),
        );
      },
      onAutoVerified: (user) {
        // Auto-verification successful (Android only)
        NavigationService.instance.navigateToHome();
      },
    );
  }
  
  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes
    ref.listen<AuthState>(authProvider, (previous, next) {
      if (next.status == AuthStatus.authenticated) {
        // Authentication successful, navigate to home with post frame callback
        WidgetsBinding.instance.addPostFrameCallback((_) {
          NavigationService.instance.navigateToHome();
        });
      } else if (next.status == AuthStatus.error) {
        // Show error message with post frame callback
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Center(
                  child: Text(
                    next.errorMessage ?? 'Verification failed',
                    style: const TextStyle(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                ),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        });
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 24.0),
          child: Column(
            children: [
              // Header
              _buildHeader(),
              
              const SizedBox(height: 32),
              
              // Title
              _buildTitle(),
              
              const SizedBox(height: 40),
              
              // OTP Input Fields
              _buildOtpFields(),

              const SizedBox(height: 48),

              // Verify Button
              _buildVerifyButton(),

              const SizedBox(height: 24),

              // Resend OTP
              _buildResendSection(),

              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Row(
      children: [
        IconButton(
          onPressed: () => NavigationService.instance.goBack(),
          icon: const Icon(
            Icons.arrow_back,
            color: Color(0xFFe92933),
            size: 24,
          ),
        ),
        const Spacer(),
        const SizedBox(width: 48), // Balance the back button
      ],
    );
  }
  
  Widget _buildTitle() {
    return const Text(
      'Enter OTP',
      style: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Color(0xFF1a1a1a),
      ),
    );
  }
  
  Widget _buildOtpFields() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(6, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 6),
          child: SizedBox(
            width: 48,
            height: 56,
            child: TextField(
              controller: _controllers[index],
              focusNode: _focusNodes[index],
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1a1a1a),
              ),
              keyboardType: TextInputType.number,
              maxLength: 1,
              decoration: InputDecoration(
                counterText: '',
                filled: true,
                fillColor: const Color(0xFFf2f2f2),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: Color(0xFFe92933),
                    width: 2,
                  ),
                ),
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              onChanged: (value) {
                if (value.length > 1) {
                  // If multiple characters are pasted, take only the first one
                  _controllers[index].text = value[0];
                  _controllers[index].selection = TextSelection.fromPosition(
                    TextPosition(offset: 1),
                  );
                }
                _onOtpChanged(value, index);
              },
              onTap: () {
                // Select all text when tapped for easy replacement
                _controllers[index].selection = TextSelection(
                  baseOffset: 0,
                  extentOffset: _controllers[index].text.length,
                );
              },
            ),
          ),
        );
      }),
    );
  }
  
  Widget _buildResendSection() {
    return Center(
      child: GestureDetector(
        onTap: _canResend ? _onResendPressed : null,
        child: Text(
          _canResend
              ? 'Resend OTP'
              : 'Resend OTP ($_resendCountdown s)',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFFe92933),
          ),
        ),
      ),
    );
  }
  
  Widget _buildVerifyButton() {
    final authState = ref.watch(authProvider);
    final isLoading = authState.isLoading || _isVerifying;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isLoading ? null : _onVerifyPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFe92933),
          foregroundColor: Colors.white,
          disabledBackgroundColor: const Color(0xFFe92933),
          disabledForegroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Verify',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}

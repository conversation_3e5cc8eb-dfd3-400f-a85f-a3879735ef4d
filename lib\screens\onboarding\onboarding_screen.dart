import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../../models/onboarding_page.dart';
import '../../services/onboarding_service.dart';
import '../../services/navigation_service.dart';
import '../../widgets/onboarding_image.dart';
import '../../utils/responsive_helper.dart';

class OnboardingScreen extends StatefulWidget {
  final VoidCallback onComplete;

  const OnboardingScreen({
    super.key,
    required this.onComplete,
  });

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  void _nextPage() {
    if (_currentPage < OnboardingData.pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _skipOnboarding() async {
    await OnboardingService.instance.completeOnboarding();
    NavigationService.instance.navigateToAlternateSignIn();
  }

  void _completeOnboarding() async {
    await OnboardingService.instance.completeOnboarding();
    widget.onComplete();
  }

  void _onLoginPressed() async {
    await OnboardingService.instance.completeOnboarding();
    NavigationService.instance.navigateToAlternateSignIn();
  }

  void _onSignUpPressed() async {
    await OnboardingService.instance.completeOnboarding();
    NavigationService.instance.navigateToAlternateRegister();
  }

  @override
  Widget build(BuildContext context) {
    final isLastPage = _currentPage == OnboardingData.pages.length - 1;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // Skip button
          SafeArea(
            bottom: false,
            child: Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: const EdgeInsets.only(top: 2.0, right: 16.0, bottom: 2.0, left: 16.0),
                child: isLastPage
                  ? const SizedBox(height: 48)
                  : TextButton(
                      onPressed: _skipOnboarding,
                      child: const Text(
                        'Skip',
                        style: TextStyle(
                          color: Color(0xFF666666),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
              ),
            ),
          ),

          // Page content
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: OnboardingData.pages.length,
              itemBuilder: (context, index) {
                final page = OnboardingData.pages[index];
                return _buildPageContent(context, page, MediaQuery.of(context).size);
              },
            ),
          ),

          // Page indicators and navigation
          SafeArea(
            top: false,
            child: Padding(
              padding: const EdgeInsets.only(top: 2.0, right: 24.0, bottom: 24.0, left: 24.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Page indicators
                  SmoothPageIndicator(
                    controller: _pageController,
                    count: OnboardingData.pages.length,
                    effect: const WormEffect(
                      dotColor: Color(0xFF666666),
                      activeDotColor: Color(0xFFe92933),
                      dotHeight: 8,
                      dotWidth: 8,
                      spacing: 16,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Navigation buttons
                  if (isLastPage) ...[
                    // Login and Sign Up buttons side by side for last page
                    Row(
                      children: [
                        Expanded(
                          child: _buildAuthButton(
                            text: 'Login',
                            onPressed: _onLoginPressed,
                            isPrimary: true,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildAuthButton(
                            text: 'Sign Up',
                            onPressed: _onSignUpPressed,
                            isPrimary: false,
                          ),
                        ),
                      ],
                    ),
                  ] else ...[
                    // Next button for other pages
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: _nextPage,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFe92933),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Next',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageContent(BuildContext context, OnboardingPage page, Size size) {
    final horizontalPadding = ResponsiveHelper.getHorizontalPadding(context);

    return Padding(
      padding: horizontalPadding,
      child: Column(
        children: [
          const SizedBox(height: 2),

          // Image placeholder (now covers full width and 70% of available space)
          OnboardingImage(
            imagePath: page.imagePath,
          ),

          const SizedBox(height: 4),

          // Text content (H1 and H2 headers) - only takes space needed for text
          _buildTextContent(context, page),

          // Fixed small spacing between text and page indicators
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildTextContent(BuildContext context, OnboardingPage page) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Title (H1) - Primary text style
        Text(
          page.title,
          style: const TextStyle(
            color: Color(0xFFe92933),
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),

        if (page.subtitle != null) ...[
          const SizedBox(height: 4),
          // Subtitle (H2) - Secondary text style
          Text(
            page.subtitle!,
            style: const TextStyle(
              color: Color(0xFF1a1a1a),
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildAuthButton({
    required String text,
    required VoidCallback onPressed,
    required bool isPrimary,
  }) {
    return SizedBox(
      height: 56,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: isPrimary ? const Color(0xFFe92933) : const Color(0xFF1565C0),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}

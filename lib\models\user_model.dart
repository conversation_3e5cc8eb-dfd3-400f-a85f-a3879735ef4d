import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';

enum AuthProvider {
  email,
  google,
  phone,
  apple,
}

class AppUser extends Equatable {
  final String id;
  final String? email;
  final String? displayName;
  final String? photoURL;
  final String? phoneNumber;
  final bool isEmailVerified;
  final AuthProvider authProvider;
  final DateTime createdAt;
  final DateTime? lastSignInAt;

  const AppUser({
    required this.id,
    this.email,
    this.displayName,
    this.photoURL,
    this.phoneNumber,
    required this.isEmailVerified,
    required this.authProvider,
    required this.createdAt,
    this.lastSignInAt,
  });

  factory AppUser.fromFirebaseUser(User user) {
    AuthProvider provider = AuthProvider.email;
    
    // Determine auth provider
    if (user.providerData.isNotEmpty) {
      final providerId = user.providerData.first.providerId;
      switch (providerId) {
        case 'google.com':
          provider = AuthProvider.google;
          break;
        case 'phone':
          provider = AuthProvider.phone;
          break;
        case 'apple.com':
          provider = AuthProvider.apple;
          break;
        default:
          provider = AuthProvider.email;
      }
    }

    return AppUser(
      id: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      phoneNumber: user.phoneNumber,
      isEmailVerified: user.emailVerified,
      authProvider: provider,
      createdAt: user.metadata.creationTime ?? DateTime.now(),
      lastSignInAt: user.metadata.lastSignInTime,
    );
  }

  AppUser copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoURL,
    String? phoneNumber,
    bool? isEmailVerified,
    AuthProvider? authProvider,
    DateTime? createdAt,
    DateTime? lastSignInAt,
  }) {
    return AppUser(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      authProvider: authProvider ?? this.authProvider,
      createdAt: createdAt ?? this.createdAt,
      lastSignInAt: lastSignInAt ?? this.lastSignInAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'phoneNumber': phoneNumber,
      'isEmailVerified': isEmailVerified,
      'authProvider': authProvider.name,
      'createdAt': createdAt.toIso8601String(),
      'lastSignInAt': lastSignInAt?.toIso8601String(),
    };
  }

  factory AppUser.fromJson(Map<String, dynamic> json) {
    return AppUser(
      id: json['id'],
      email: json['email'],
      displayName: json['displayName'],
      photoURL: json['photoURL'],
      phoneNumber: json['phoneNumber'],
      isEmailVerified: json['isEmailVerified'] ?? false,
      authProvider: AuthProvider.values.firstWhere(
        (e) => e.name == json['authProvider'],
        orElse: () => AuthProvider.email,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      lastSignInAt: json['lastSignInAt'] != null 
          ? DateTime.parse(json['lastSignInAt']) 
          : null,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        displayName,
        photoURL,
        phoneNumber,
        isEmailVerified,
        authProvider,
        createdAt,
        lastSignInAt,
      ];
}
